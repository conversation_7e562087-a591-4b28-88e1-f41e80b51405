import { test, expect } from '@playwright/test';
import { loginAs } from '../../utils/auth-utils';
import { Role } from '../../../src/[types]/Role';
import { MainLayoutPage } from '../../pages/Layout/main-layout.page';
import ROUTES from '../../../src/Routing/appRoutes';

// Define test IDs globally for use in rolePermissions and getAllNavigableRoutes
const testId = 'test-id';
const testClinicId = 'test-clinic-id';
const testDoctorId = 'test-doctor-id';
const testUserId = 'test-user-id';
const testPurchaseId = 'test-purchase-id';

// Utility: Generate all static and sample dynamic routes from ROUTES object
function getAllNavigableRoutes() {
  // Helper for dynamic routes: use 'test-id' or 'test-clinic-id' as placeholder
  // testId, testClinicId, testDoctorId, testUserId, testPurchaseId are now globally defined

  return [
    // HOMEPAGES
    ROUTES.HOMEPAGES.ADMIN,
    ROUTES.HOMEPAGES.CLINIC_ADMIN,
    ROUTES.HOMEPAGES.DOCTOR,
    ROUTES.HOMEPAGES.CLIENT,
    ROUTES.HOMEPAGES.PATIENT,
    // USERS
    ROUTES.USERS.LIST,
    ROUTES.USERS.DETAILS(testId),
    ROUTES.USERS.EDIT(testId),
    ROUTES.USERS.ADD,
    ROUTES.USERS.MIGRATION,
    // PATIENTS
    ROUTES.PATIENTS.HOME,
    ROUTES.PATIENTS.LIST,
    ROUTES.PATIENTS.DETAILS(testId),
    ROUTES.PATIENTS.EDIT(testId),
    ROUTES.PATIENTS.PROFILE(testId),
    ROUTES.PATIENTS.ADD,
    // CLIENTS
    ROUTES.CLIENTS.HOME,
    ROUTES.CLIENTS.LIST,
    ROUTES.CLIENTS.DETAILS(testId),
    ROUTES.CLIENTS.EDIT(testId),
    ROUTES.CLIENTS.PROFILE(testId),
    ROUTES.CLIENTS.ADD,
    ROUTES.CLIENTS.ADD_PATIENT,
    ROUTES.CLIENTS.BILLING,
    // DOCTORS
    ROUTES.DOCTORS.HOME,
    ROUTES.DOCTORS.LIST,
    ROUTES.DOCTORS.DETAILS(testId),
    ROUTES.DOCTORS.EDIT(testId),
    ROUTES.DOCTORS.PROFILE(testId),
    ROUTES.DOCTORS.ADD,
    ROUTES.DOCTORS.MY_PATIENTS,
    // CLINIC_ADMINS
    ROUTES.CLINIC_ADMINS.HOME,
    ROUTES.CLINIC_ADMINS.LIST,
    ROUTES.CLINIC_ADMINS.DETAILS(testId),
    ROUTES.CLINIC_ADMINS.EDIT(testId),
    ROUTES.CLINIC_ADMINS.PROFILE,
    ROUTES.CLINIC_ADMINS.ADD,
    ROUTES.CLINICS.MY_CLINIC,
    // CLINICS
    ROUTES.CLINICS.LIST,
    ROUTES.CLINICS.DETAILS(testId),
    ROUTES.CLINICS.EDIT(testId),
    ROUTES.CLINICS.ADD,
    // QUESTIONNAIRES
    ROUTES.QUESTIONNAIRES.LIST,
    ROUTES.QUESTIONNAIRES.MY_QUESTIONNAIRES,
    ROUTES.QUESTIONNAIRES.DETAILS(testId),
    ROUTES.QUESTIONNAIRES.WIZARD(testId),
    // COMPLIANCE_REPORTS
    ROUTES.COMPLIANCE_REPORTS.LIST,
    ROUTES.COMPLIANCE_REPORTS.DETAILS(testId),
    ROUTES.COMPLIANCE_REPORTS.EDIT(testId),
    ROUTES.COMPLIANCE_REPORTS.REVIEW(testId),
    // ADMIN
    ROUTES.ADMIN.HOME,
    ROUTES.ADMIN.ROLES,
    ROUTES.ADMIN.PERMISSIONS,
    ROUTES.ADMIN.DASHBOARD,
    ROUTES.ADMIN.ONBOARDING,
    // OTHERS
    ROUTES.OTHERS.ANALYTICS,
    ROUTES.OTHERS.SETTINGS,
    ROUTES.OTHERS.HEALTH_INSIGHTS,
    ROUTES.OTHERS.APPOINTMENTS,
    ROUTES.OTHERS.MESSAGES,
    ROUTES.OTHERS.MEDICAL_RECORDS,
    ROUTES.OTHERS.REPORTS,
    ROUTES.OTHERS.HOME,
    // PRODUCTS
    ROUTES.PRODUCTS.LIST,
    ROUTES.PRODUCTS.ADD,
    ROUTES.PRODUCTS.EDIT(testId),
    ROUTES.PRODUCTS.DETAILS(testId),
    // AI_CHAT
    ROUTES.AI_CHAT.BASE,
    // PURCHASES
    ROUTES.PURCHASES.LIST,
    ROUTES.PURCHASES.CART,
    ROUTES.PURCHASES.CHECKOUT,
    ROUTES.PURCHASES.MY_PURCHASES,
    ROUTES.PURCHASES.DETAILS(testPurchaseId),
    ROUTES.PURCHASES.EDIT(testPurchaseId),
    ROUTES.PURCHASES.ADD
  ];
}

// --- Role Permissions Matrix ---
const rolePermissions = {
  Admin: new Set([
    // Admin has comprehensive access to most routes
    // HOMEPAGES - Admin can access all role homepages
    ROUTES.HOMEPAGES.ADMIN,
    ROUTES.HOMEPAGES.CLINIC_ADMIN,
    ROUTES.HOMEPAGES.DOCTOR,
    ROUTES.HOMEPAGES.CLIENT,
    ROUTES.HOMEPAGES.PATIENT,
    // USERS - Admin can manage users
    ROUTES.USERS.LIST,
    ROUTES.USERS.ADD,
    ROUTES.USERS.MIGRATION,
    // Note: User details/edit with test IDs redirect properly, so excluding them
    // PATIENTS - Admin can manage patients
    ROUTES.PATIENTS.HOME,
    ROUTES.PATIENTS.LIST,
    ROUTES.PATIENTS.ADD,
    // CLIENTS - Admin can manage clients
    ROUTES.CLIENTS.HOME,
    ROUTES.CLIENTS.LIST,
    ROUTES.CLIENTS.ADD,
    ROUTES.CLIENTS.ADD_PATIENT,
    ROUTES.CLIENTS.BILLING,
    // DOCTORS - Admin can manage doctors
    ROUTES.DOCTORS.HOME,
    ROUTES.DOCTORS.LIST,
    ROUTES.DOCTORS.ADD,
    ROUTES.DOCTORS.MY_PATIENTS,
    // CLINIC_ADMINS - Admin can manage clinic admins
    ROUTES.CLINIC_ADMINS.HOME,
    ROUTES.CLINIC_ADMINS.LIST,
    ROUTES.CLINIC_ADMINS.ADD,
    ROUTES.CLINIC_ADMINS.PROFILE,
    ROUTES.CLINICS.MY_CLINIC,
    // CLINICS - Admin can manage clinics
    ROUTES.CLINICS.LIST,
    ROUTES.CLINICS.ADD,
    // QUESTIONNAIRES - Admin can manage questionnaires
    ROUTES.QUESTIONNAIRES.LIST,
    ROUTES.QUESTIONNAIRES.MY_QUESTIONNAIRES,
    // COMPLIANCE_REPORTS - Admin can manage compliance reports
    ROUTES.COMPLIANCE_REPORTS.LIST,
    // ADMIN - Admin specific routes
    ROUTES.ADMIN.HOME,
    ROUTES.ADMIN.ROLES,
    ROUTES.ADMIN.PERMISSIONS,
    ROUTES.ADMIN.DASHBOARD,
    ROUTES.ADMIN.ONBOARDING,
    // OTHERS - General routes
    ROUTES.OTHERS.ANALYTICS,
    ROUTES.OTHERS.SETTINGS,
    ROUTES.OTHERS.HEALTH_INSIGHTS,
    ROUTES.OTHERS.APPOINTMENTS,
    ROUTES.OTHERS.MESSAGES,
    ROUTES.OTHERS.MEDICAL_RECORDS,
    ROUTES.OTHERS.REPORTS,
    ROUTES.OTHERS.HOME,
    // PRODUCTS - Admin can manage products
    ROUTES.PRODUCTS.LIST,
    ROUTES.PRODUCTS.ADD,
    // AI_CHAT - Admin can access AI chat
    ROUTES.AI_CHAT.BASE,
    // PURCHASES - Admin can manage purchases
    ROUTES.PURCHASES.LIST,
    ROUTES.PURCHASES.CART,
    ROUTES.PURCHASES.CHECKOUT,
    ROUTES.PURCHASES.MY_PURCHASES,
    ROUTES.PURCHASES.ADD
  ]),

  ClinicAdmin: new Set([
    // ClinicAdmin has access to their own homepage and clinic management
    ROUTES.HOMEPAGES.CLINIC_ADMIN,
    ROUTES.CLINIC_ADMINS.HOME,
    ROUTES.CLINIC_ADMINS.PROFILE,
    ROUTES.CLINICS.MY_CLINIC,
    // ClinicAdmin can manage patients and doctors in their clinic
    ROUTES.PATIENTS.HOME,
    ROUTES.PATIENTS.LIST,
    ROUTES.DOCTORS.HOME,
    ROUTES.DOCTORS.LIST,
    ROUTES.DOCTORS.MY_PATIENTS,
    // ClinicAdmin can access questionnaires
    ROUTES.QUESTIONNAIRES.LIST,
    ROUTES.QUESTIONNAIRES.MY_QUESTIONNAIRES,
    // ClinicAdmin can access general features
    ROUTES.OTHERS.SETTINGS,
    ROUTES.OTHERS.APPOINTMENTS,
    ROUTES.OTHERS.MESSAGES,
    ROUTES.OTHERS.REPORTS,
    ROUTES.OTHERS.HOME,
    // Based on test results, ClinicAdmin can access users
    ROUTES.USERS.LIST,
    ROUTES.USERS.ADD
  ]),

  Doctor: new Set([
    // Doctor has access to their own homepage and patient management
    ROUTES.HOMEPAGES.DOCTOR,
    ROUTES.DOCTORS.HOME,
    ROUTES.DOCTORS.MY_PATIENTS,
    // Doctor can manage their patients
    ROUTES.PATIENTS.HOME,
    ROUTES.PATIENTS.LIST,
    // Doctor can access questionnaires
    ROUTES.QUESTIONNAIRES.LIST,
    ROUTES.QUESTIONNAIRES.MY_QUESTIONNAIRES,
    // Doctor can access general features
    ROUTES.OTHERS.SETTINGS,
    ROUTES.OTHERS.HEALTH_INSIGHTS,
    ROUTES.OTHERS.APPOINTMENTS,
    ROUTES.OTHERS.MESSAGES,
    ROUTES.OTHERS.MEDICAL_RECORDS,
    ROUTES.OTHERS.HOME,
    // Doctor can access AI chat
    ROUTES.AI_CHAT.BASE
  ]),

  Client: new Set([
    // Client has access to their own homepage and features
    ROUTES.HOMEPAGES.CLIENT,
    ROUTES.CLIENTS.HOME,
    ROUTES.CLIENTS.BILLING,
    // Client can access products and purchases
    ROUTES.PRODUCTS.LIST,
    ROUTES.PURCHASES.CART,
    ROUTES.PURCHASES.CHECKOUT,
    ROUTES.PURCHASES.MY_PURCHASES,
    // Client can access questionnaires
    ROUTES.QUESTIONNAIRES.MY_QUESTIONNAIRES,
    // Client can access general features
    ROUTES.OTHERS.SETTINGS,
    ROUTES.OTHERS.APPOINTMENTS,
    ROUTES.OTHERS.MESSAGES,
    ROUTES.OTHERS.HOME,
    // Client can access AI chat
    ROUTES.AI_CHAT.BASE
  ]),

  Patient: new Set([
    ROUTES.HOMEPAGES.PATIENT,            // Equivalent to ROUTES.PATIENTS.HOME
    ROUTES.PATIENTS.HOME,
    ROUTES.PATIENTS.DETAILS(testId),     // Patient viewing their own details
    ROUTES.PATIENTS.EDIT(testId),        // Patient editing their own details
    ROUTES.PATIENTS.PROFILE(testId),     // Patient viewing their own profile
    ROUTES.QUESTIONNAIRES.MY_QUESTIONNAIRES,
    ROUTES.QUESTIONNAIRES.DETAILS(testId), // Viewing details of one of their questionnaires
    ROUTES.QUESTIONNAIRES.WIZARD(testId),    // Filling out one of their questionnaires
    ROUTES.COMPLIANCE_REPORTS.REVIEW(testId), // Assuming patient can review *their* specific compliance report
    ROUTES.OTHERS.SETTINGS,
    ROUTES.OTHERS.HEALTH_INSIGHTS,
    ROUTES.OTHERS.APPOINTMENTS,
    ROUTES.OTHERS.MESSAGES,              // Patient's messages
    ROUTES.OTHERS.MEDICAL_RECORDS,       // Patient's medical records
    ROUTES.OTHERS.HOME,                  // General home, likely redirects to patient dashboard
    ROUTES.AI_CHAT.BASE,                 // Patient's AI chat feature
    ROUTES.PURCHASES.CART,               // Patient's shopping cart
    ROUTES.PURCHASES.CHECKOUT,           // Patient checkout process
    ROUTES.PURCHASES.MY_PURCHASES,       // List of patient's own purchases
    ROUTES.PURCHASES.DETAILS(testPurchaseId), // Details of a specific purchase
    ROUTES.PURCHASES.EDIT(testPurchaseId)    // Editing details of a specific purchase
  ])
};

// --- Test Suite ---
const allRoutes = [...new Set(getAllNavigableRoutes())];

[Role.Admin, Role.ClinicAdmin, Role.Doctor, Role.Client, Role.Patient].forEach((role) => {
  test.describe(`${role} navigation RBAC`, () => {
    let mainLayoutPage;
    test.beforeEach(async ({ page, baseURL }) => {
      mainLayoutPage = new MainLayoutPage(page);
      await loginAs(page, role, baseURL);
      await mainLayoutPage.openAllMenuGroups();
    });

    allRoutes.forEach((route) => {
      const isAllowed = rolePermissions[role].has(route);
      test(`should ${isAllowed ? '' : 'NOT '}allow ${role} to access ${route}`, async ({ page }) => {
        await page.goto(route);
        if (isAllowed) {
          // Expect allowed: page loads, e.g. no unauthorized error
          await expect(page).not.toHaveURL(/unauthorized|login|error|404/i, { timeout: 10000 });
        } else {
          // Expect forbidden: redirected or error/unauthorized page
          await expect(page).toHaveURL(/unauthorized|login|error|404/i, { timeout: 10000 });
        }
      });
    });
  });
});
