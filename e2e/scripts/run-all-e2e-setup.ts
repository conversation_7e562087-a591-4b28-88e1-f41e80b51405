import { spawnSync } from 'child_process';

function runScript(name: string, script: string) {
  console.log(`\n=== Running ${name} ===`);
  const result = spawnSync('npx', ['tsx', script], { stdio: 'inherit' });
  if (result.status !== 0) {
    throw new Error(`${name} failed.`);
  }
}

function main() {
  runScript('Create Test Users', 'scripts/create-test-users.ts');
  runScript('Create Questionnaire Templates', 'scripts/create-questionnaire-templates.ts');
  runScript('Create Purchases', 'scripts/create-purchases.ts');
  runScript('Create Questionnaires', 'scripts/create-questionnaires.ts');
  console.log('\n✅ All E2E setup scripts completed successfully.');
}

main();
