import React, { useState } from 'react';
import {
  Box,
  Button,
  Typography,
  Paper,
  Divider,
  List,
  ListItem,
  ListItemText,
  Alert,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  SelectChangeEvent
} from '@mui/material';
import MainCard from '[components]/cards/MainCard';

/**
 * This component provides utilities to help debug and fix import issues in the RBAC system.
 * It's meant to be used by developers to diagnose path-related problems.
 */
const FixImports = () => {
  const [log, setLog] = useState<string[]>([]);
  const [selectedFix, setSelectedFix] = useState<string>('path-consistency');
  const [status, setStatus] = useState<'idle' | 'running' | 'completed' | 'error'>('idle');

  const logMessage = (message: string) => {
    setLog((prev) => [...prev, message]);
  };

  const handleFixSelection = (event: SelectChangeEvent) => {
    setSelectedFix(event.target.value);
  };

  const runFix = () => {
    setStatus('running');
    setLog([]);
    logMessage(`Starting fix: ${selectedFix}`);

    try {
      switch (selectedFix) {
        case 'path-consistency':
          logMessage('Checking import path consistency...');
          logMessage('✅ This would scan for inconsistent imports between @features and [features]');
          logMessage('✅ Looking for inconsistent service imports');
          logMessage('✅ Examining hook import patterns');
          break;
        case 'permission-checks':
          logMessage('Verifying permission checks...');
          logMessage('✅ Checking if Admin has all permissions');
          logMessage('✅ Validating role hierarchy in permission checks');
          break;
        case 'role-context':
          logMessage('Debugging RoleContext issues...');
          logMessage('✅ Verifying RoleContext is properly initialized');
          logMessage('✅ Checking if canAssumeRoles flag is set correctly');
          break;
        case 'migrate-to-features':
          logMessage('Starting migration from @features to [features]...');
          logMessage('✅ Scanning for @features import statements');
          logMessage('✅ This would replace @features/RBAC with [features]/RBAC in imports');
          logMessage('❗ IMPORTANT: We are standardizing on [features] syntax for all feature imports');
          logMessage('❗ @features paths should not be used and are being removed');
          break;
        default:
          logMessage('Unknown fix selected');
      }

      // Simulate completion after processing
      setTimeout(() => {
        logMessage('Fix completed');
        setStatus('completed');
      }, 1500);
    } catch (error) {
      logMessage(`Error: ${error}`);
      setStatus('error');
    }
  };

  return (
    <MainCard title="RBAC Import Fixer">
      <Typography variant="body1" paragraph>
        This utility helps diagnose and fix common RBAC import and path-related issues.
      </Typography>

      <Box sx={{ mb: 3 }}>
        <FormControl fullWidth sx={{ mb: 2 }}>
          <InputLabel>Select Fix</InputLabel>
          <Select value={selectedFix} onChange={handleFixSelection} label="Select Fix">
            <MenuItem value="path-consistency">Fix Import Path Consistency</MenuItem>
            <MenuItem value="permission-checks">Validate Permission Checks</MenuItem>
            <MenuItem value="role-context">Debug RoleContext Issues</MenuItem>
            <MenuItem value="migrate-to-features">Migrate @features to [features]</MenuItem>
          </Select>
        </FormControl>

        <Button variant="contained" color="primary" onClick={runFix} disabled={status === 'running'}>
          {status === 'running' ? 'Running...' : 'Run Fix'}
        </Button>
      </Box>

      <Divider sx={{ my: 2 }} />

      <Typography variant="h5" gutterBottom>
        Fix Log
      </Typography>

      <Paper variant="outlined" sx={{ p: 2, height: '300px', overflow: 'auto', bgcolor: 'background.default' }}>
        <List dense>
          {log.length === 0 ? (
            <ListItem>
              <ListItemText primary="No logs yet. Run a fix to see output." />
            </ListItem>
          ) : (
            log.map((message, index) => (
              <ListItem key={index} sx={{ py: 0.5 }}>
                <ListItemText
                  primary={message}
                  primaryTypographyProps={{
                    fontFamily: 'monospace',
                    fontSize: '0.875rem'
                  }}
                />
              </ListItem>
            ))
          )}
        </List>
      </Paper>

      {status === 'completed' && (
        <Alert severity="success" sx={{ mt: 2 }}>
          Fix operation completed. Check the logs for details.
        </Alert>
      )}

      {status === 'error' && (
        <Alert severity="error" sx={{ mt: 2 }}>
          Error occurred during fix operation. Check the logs for details.
        </Alert>
      )}

      <Box sx={{ mt: 3 }}>
        <Typography variant="h5" gutterBottom>
          Common RBAC Import Issues
        </Typography>
        <Typography variant="body2" paragraph>
          1. <strong>Inconsistent Feature Paths</strong>: We've standardized on <code>[features]</code> syntax. Do not use{' '}
          <code>@features</code> paths anymore.
        </Typography>
        <Typography variant="body2" paragraph>
          2. <strong>Nested Path Issues</strong>: Using [hooks] or [services] incorrectly
        </Typography>
        <Typography variant="body2" paragraph>
          3. <strong>Missing Components</strong>: Failing to properly import RoleContext or AssumeRoleSelector
        </Typography>
        <Typography variant="body2">
          Go to <code>/trq/admin/rbac-debug</code> for detailed RBAC diagnostics.
        </Typography>
      </Box>
    </MainCard>
  );
};

export default FixImports;
