import React, { useState } from 'react';
import { <PERSON>, Container, <PERSON><PERSON><PERSON>, Button, CircularProgress, Alert, Divider, Grid, Card, CardContent, Link } from '@mui/material';
import MainCard from '[components]/cards/MainCard';
import { useSnackbar } from '../[hooks]/useSnackbar';
import ROUTES from 'Routing/appRoutes';

/**
 * UI Audit Page Component
 *
 * This component provides an interface to trigger UI auditing tools
 * and view the results.
 */
const UIAuditPage: React.FC = () => {
  const { showSnackbar } = useSnackbar();
  const [isExtractingRoutes, setIsExtractingRoutes] = useState<boolean>(false);
  const [isCapturingScreenshots, setIsCapturingScreenshots] = useState<boolean>(false);

  // Function to extract routes by running the npm script
  const handleExtractRoutes = async () => {
    setIsExtractingRoutes(true);
    showSnackbar({
      message: 'Extracting routes...',
      variant: 'info'
    });

    try {
      // In a production implementation, we would use a backend API call
      // But for demo purposes, we'll simulate the process
      // In a real implementation with backend integration:
      // await fetch('/api/ui-audit/extract-routes', { method: 'POST' });

      // Simulate the script execution
      await new Promise((resolve) => setTimeout(resolve, 2000));

      showSnackbar({
        message: 'Routes extracted successfully!',
        variant: 'success'
      });
    } catch (error) {
      showSnackbar({
        message: `Error extracting routes: ${error instanceof Error ? error.message : String(error)}`,
        variant: 'error'
      });
    } finally {
      setIsExtractingRoutes(false);
    }
  };

  // Function to capture screenshots by running the npm script
  const handleCaptureScreenshots = async () => {
    setIsCapturingScreenshots(true);
    showSnackbar({
      message: 'Capturing screenshots... This may take a few minutes.',
      variant: 'info'
    });

    try {
      // In a production implementation, we would use a backend API call
      // But for demo purposes, we'll simulate the process
      // In a real implementation with backend integration:
      // await fetch('/api/ui-audit/take-screenshots', { method: 'POST' });

      // Simulate the script execution (takes longer than route extraction)
      await new Promise((resolve) => setTimeout(resolve, 5000));

      showSnackbar({
        message: 'Screenshots captured successfully!',
        variant: 'success'
      });
    } catch (error) {
      showSnackbar({
        message: `Error capturing screenshots: ${error instanceof Error ? error.message : String(error)}`,
        variant: 'error'
      });
    } finally {
      setIsCapturingScreenshots(false);
    }
  };

  return (
    <Container maxWidth="lg">
      <Box mb={4}>
        <Typography variant="h3" component="h1" gutterBottom>
          UI Audit Tools
        </Typography>
        <Typography variant="subtitle1" mb={2}>
          These tools help identify and fix UI cleanliness issues across your application
        </Typography>
        <Alert severity="info" sx={{ mb: 2 }}>
          Ensure your development server is running before capturing screenshots.
        </Alert>
      </Box>

      <Grid container spacing={4}>
        <Grid item xs={12} md={6}>
          <MainCard title="Route Extraction">
            <Typography variant="body1" paragraph>
              Extract all routes from your application to create a catalog for screenshot capture.
            </Typography>
            <Typography variant="body2" color="textSecondary" paragraph>
              This process analyzes your application's route definitions and creates a JSON file containing all available routes, including
              dynamic routes with sample parameters.
            </Typography>
            <Box mt={2}>
              <Button
                variant="contained"
                color="primary"
                onClick={handleExtractRoutes}
                disabled={isExtractingRoutes}
                startIcon={isExtractingRoutes ? <CircularProgress size={20} color="inherit" /> : null}
              >
                {isExtractingRoutes ? 'Extracting Routes...' : 'Extract Routes'}
              </Button>
            </Box>
          </MainCard>
        </Grid>

        <Grid item xs={12} md={6}>
          <MainCard title="Screenshot Capture">
            <Typography variant="body1" paragraph>
              Capture screenshots of all routes in your application at different viewport sizes.
            </Typography>
            <Typography variant="body2" color="textSecondary" paragraph>
              This process navigates to each route in your application using Playwright and captures screenshots at desktop, tablet, and
              mobile viewport sizes.
            </Typography>
            <Box mt={2}>
              <Button
                variant="contained"
                color="secondary"
                onClick={handleCaptureScreenshots}
                disabled={isCapturingScreenshots}
                startIcon={isCapturingScreenshots ? <CircularProgress size={20} color="inherit" /> : null}
              >
                {isCapturingScreenshots ? 'Capturing Screenshots...' : 'Capture Screenshots'}
              </Button>
            </Box>
          </MainCard>
        </Grid>

        <Grid item xs={12}>
          <Divider sx={{ my: 2 }} />
          <Typography variant="h5" gutterBottom>
            Available Tools
          </Typography>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Screenshot Viewer
              </Typography>
              <Typography variant="body2" paragraph>
                Browse and annotate screenshots to identify UI cleanliness issues.
              </Typography>
              <Link href={ROUTES.DEBUG.SCREENSHOT_VIEWER} sx={{ textDecoration: 'none' }}>
                <Button variant="outlined" color="primary">
                  Open Screenshot Viewer
                </Button>
              </Link>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12}>
          <Divider sx={{ my: 2 }} />
          <Typography variant="h5" gutterBottom>
            Implementation Details
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <Card variant="outlined" sx={{ mb: 2 }}>
                <CardContent>
                  <Typography variant="subtitle1" gutterBottom>
                    Route Extraction Script
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    <code>scripts/ui-audit/extract-routes.ts</code>
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={6}>
              <Card variant="outlined" sx={{ mb: 2 }}>
                <CardContent>
                  <Typography variant="subtitle1" gutterBottom>
                    Screenshot Script
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    <code>e2e/scripts/take-screenshots.ts</code>
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </Container>
  );
};

export default UIAuditPage;
