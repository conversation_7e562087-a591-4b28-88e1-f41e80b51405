e2e;
import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  CardMedia,
  Tabs,
  Tab,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Paper,
  Divider,
  CircularProgress,
  Tooltip,
  useTheme
} from '@mui/material';
import MainCard from '[components]/cards/MainCard';
import { useSnackbar } from '../[hooks]/useSnackbar';

// Define interfaces for screenshot data
interface ScreenshotInfo {
  routeName: string;
  path: string;
  viewportSize: string;
  screenshotPath: string;
  timestamp: string;
  role: string;
}

interface UIIssue {
  id: string;
  routeName: string;
  viewportSize: string;
  screenshotPath: string;
  description: string;
  status: 'open' | 'in-progress' | 'fixed' | 'wont-fix';
  coordinates: { x: number; y: number };
  createdAt: string;
  assignedTo?: string;
}

/**
 * Screenshot Viewer Page Component
 *
 * This component displays screenshots taken by the Playwright screenshot tool and
 * allows users to identify and track UI issues.
 */
const ScreenshotViewerPage: React.FC = () => {
  const theme = useTheme();
  const { showSnackbar } = useSnackbar();

  // State for screenshots and issues
  const [screenshots, setScreenshots] = useState<ScreenshotInfo[]>([]);
  const [filteredScreenshots, setFilteredScreenshots] = useState<ScreenshotInfo[]>([]);
  const [manifests, setManifests] = useState<string[]>([]);
  const [selectedManifest, setSelectedManifest] = useState<string>('');
  const [modules, setModules] = useState<string[]>([]);
  const [selectedModule, setSelectedModule] = useState<string>('');
  const [viewportFilter, setViewportFilter] = useState<string>('all');
  const [roleFilter, setRoleFilter] = useState<string>('all');
  const [issues, setIssues] = useState<UIIssue[]>([]);
  const [selectedScreenshot, setSelectedScreenshot] = useState<ScreenshotInfo | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [newIssue, setNewIssue] = useState<Partial<UIIssue>>({
    description: '',
    status: 'open',
    coordinates: { x: 0, y: 0 }
  });

  // Mock function to load manifests (would be replaced with actual API/fetch call)
  const loadManifests = () => {
    // In a real implementation, this would be fetched from the server
    setManifests(['manifest-2025-05-01T07-30-00.json']);
    setSelectedManifest('manifest-2025-05-01T07-30-00.json');
    setIsLoading(false);
  };

  // Mock function to load screenshots from a manifest (would be replaced with actual API/fetch call)
  const loadScreenshotsFromManifest = (manifestName: string) => {
    // For demo purposes, we'll create mock data
    const mockScreenshots: ScreenshotInfo[] = [
      {
        routeName: 'PATIENTS.LIST',
        path: '/trq/patients',
        viewportSize: 'desktop',
        screenshotPath: 'patients/patients-list/admin-desktop-2025-05-01T07-30-00.png',
        timestamp: '2025-05-01T07-30-00',
        role: 'admin'
      },
      {
        routeName: 'PATIENTS.DETAILS',
        path: '/trq/patients/sample-id/details',
        viewportSize: 'desktop',
        screenshotPath: 'patients/patients-details/admin-desktop-2025-05-01T07-30-00.png',
        timestamp: '2025-05-01T07-30-00',
        role: 'admin'
      },
      {
        routeName: 'DOCTORS.MY_PATIENTS',
        path: '/trq/doctors/my-patients',
        viewportSize: 'desktop',
        screenshotPath: 'doctors/doctors-my_patients/doctor-desktop-2025-05-01T07-30-00.png',
        timestamp: '2025-05-01T07-30-00',
        role: 'doctor'
      },
      {
        routeName: 'PATIENTS.LIST',
        path: '/trq/patients',
        viewportSize: 'mobile',
        screenshotPath: 'patients/patients-list/admin-mobile-2025-05-01T07-30-00.png',
        timestamp: '2025-05-01T07-30-00',
        role: 'admin'
      }
    ];

    setScreenshots(mockScreenshots);

    // Extract unique modules
    const uniqueModules = Array.from(new Set(mockScreenshots.map((s) => s.routeName.split('.')[0])));
    setModules(uniqueModules);

    // Initial filtering
    setFilteredScreenshots(mockScreenshots);
  };

  // Load manifests on component mount
  useEffect(() => {
    loadManifests();
  }, []);

  // Load screenshots when a manifest is selected
  useEffect(() => {
    if (selectedManifest) {
      loadScreenshotsFromManifest(selectedManifest);
    }
  }, [selectedManifest]);

  // Handle filtering
  useEffect(() => {
    let filtered = [...screenshots];

    if (selectedModule) {
      filtered = filtered.filter((s) => s.routeName.startsWith(selectedModule));
    }

    if (viewportFilter !== 'all') {
      filtered = filtered.filter((s) => s.viewportSize === viewportFilter);
    }

    if (roleFilter !== 'all') {
      filtered = filtered.filter((s) => s.role === roleFilter);
    }

    setFilteredScreenshots(filtered);
  }, [selectedModule, viewportFilter, roleFilter, screenshots]);

  // Handle manifest selection
  const handleManifestChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    setSelectedManifest(event.target.value as string);
  };

  // Handle module selection
  const handleModuleChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    setSelectedModule(event.target.value as string);
  };

  // Handle viewport filter change
  const handleViewportChange = (_event: React.ChangeEvent<{}>, newValue: string) => {
    setViewportFilter(newValue);
  };

  // Handle role filter change
  const handleRoleChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    setRoleFilter(event.target.value as string);
  };

  // Handle screenshot selection
  const handleScreenshotSelect = (screenshot: ScreenshotInfo) => {
    setSelectedScreenshot(screenshot);
    setNewIssue({
      description: '',
      status: 'open',
      coordinates: { x: 0, y: 0 }
    });
  };

  // Handle image click to set coordinates
  const handleImageClick = (event: React.MouseEvent<HTMLImageElement>) => {
    const rect = event.currentTarget.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    setNewIssue((prev) => ({
      ...prev,
      coordinates: { x, y }
    }));
  };

  // Handle issue description change
  const handleDescriptionChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setNewIssue((prev) => ({
      ...prev,
      description: event.target.value
    }));
  };

  // Create new issue
  const handleCreateIssue = () => {
    if (!selectedScreenshot || !newIssue.description) return;

    const issue: UIIssue = {
      id: `issue-${Date.now()}`,
      routeName: selectedScreenshot.routeName,
      viewportSize: selectedScreenshot.viewportSize,
      screenshotPath: selectedScreenshot.screenshotPath,
      description: newIssue.description || '',
      status: 'open',
      coordinates: newIssue.coordinates || { x: 0, y: 0 },
      createdAt: new Date().toISOString()
    };

    const updatedIssues = [...issues, issue];
    setIssues(updatedIssues);

    // In a real implementation, you would save this to your server
    // For now, just show a success message
    showSnackbar({
      message: 'UI issue created successfully',
      variant: 'success'
    });

    // Reset form
    setNewIssue({
      description: '',
      status: 'open',
      coordinates: { x: 0, y: 0 }
    });
  };

  // Handle running screenshot capture
  const handleTakeScreenshots = () => {
    showSnackbar({
      message: 'Screenshot capture started. This may take a few minutes.',
      variant: 'info'
    });

    // In a real implementation, this would trigger the screenshot script
    // For now, just show a mock success after a delay
    setTimeout(() => {
      showSnackbar({
        message: 'Screenshots captured successfully!',
        variant: 'success'
      });
      // Reload manifests
      loadManifests();
    }, 3000);
  };

  return (
    <Container maxWidth="xl">
      <Box mb={4}>
        <Typography variant="h3" component="h1" gutterBottom>
          UI Screenshot Viewer
        </Typography>
        <Typography variant="subtitle1" gutterBottom>
          Browse and annotate UI screenshots to track and fix cleanliness issues
        </Typography>

        <Box mt={2} mb={4}>
          <Button variant="contained" color="primary" onClick={handleTakeScreenshots}>
            Capture New Screenshots
          </Button>
        </Box>
      </Box>

      {isLoading ? (
        <Box display="flex" justifyContent="center" alignItems="center" height="300px">
          <CircularProgress />
        </Box>
      ) : (
        <Grid container spacing={3}>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Filters
                </Typography>

                <Box mb={3}>
                  <FormControl fullWidth>
                    <InputLabel>Screenshot Set</InputLabel>
                    <Select value={selectedManifest} onChange={handleManifestChange as any} label="Screenshot Set">
                      {manifests.map((manifest) => (
                        <MenuItem key={manifest} value={manifest}>
                          {manifest.replace('manifest-', '').replace('.json', '')}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Box>

                <Box mb={3}>
                  <FormControl fullWidth>
                    <InputLabel>Module</InputLabel>
                    <Select value={selectedModule} onChange={handleModuleChange as any} label="Module">
                      <MenuItem value="">All Modules</MenuItem>
                      {modules.map((module) => (
                        <MenuItem key={module} value={module}>
                          {module}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Box>

                <Box mb={3}>
                  <Typography variant="subtitle2" gutterBottom>
                    Viewport
                  </Typography>
                  <Tabs value={viewportFilter} onChange={handleViewportChange} variant="fullWidth" sx={{ mb: 2 }}>
                    <Tab label="All" value="all" />
                    <Tab label="Desktop" value="desktop" />
                    <Tab label="Tablet" value="tablet" />
                    <Tab label="Mobile" value="mobile" />
                  </Tabs>
                </Box>

                <Box mb={3}>
                  <FormControl fullWidth>
                    <InputLabel>Role</InputLabel>
                    <Select value={roleFilter} onChange={handleRoleChange as any} label="Role">
                      <MenuItem value="all">All Roles</MenuItem>
                      <MenuItem value="admin">Admin</MenuItem>
                      <MenuItem value="doctor">Doctor</MenuItem>
                      <MenuItem value="patient">Patient</MenuItem>
                      <MenuItem value="client">Client</MenuItem>
                      <MenuItem value="clinicAdmin">Clinic Admin</MenuItem>
                    </Select>
                  </FormControl>
                </Box>

                <Typography variant="subtitle2" gutterBottom>
                  {filteredScreenshots.length} screenshots found
                </Typography>
              </CardContent>
            </Card>

            <Box mt={3}>
              <Typography variant="h6" gutterBottom>
                Screenshots
              </Typography>

              {filteredScreenshots.map((screenshot, index) => (
                <Card
                  key={`${screenshot.routeName}-${screenshot.viewportSize}-${index}`}
                  sx={{ mb: 2, cursor: 'pointer' }}
                  onClick={() => handleScreenshotSelect(screenshot)}
                >
                  <CardContent>
                    <Typography variant="subtitle2" noWrap>
                      {screenshot.routeName}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      {screenshot.path}
                    </Typography>
                    <Box display="flex" mt={1}>
                      <Chip label={screenshot.viewportSize} size="small" sx={{ mr: 1 }} />
                      <Chip label={screenshot.role} size="small" />
                    </Box>
                  </CardContent>
                </Card>
              ))}
            </Box>
          </Grid>

          <Grid item xs={12} md={9}>
            {selectedScreenshot ? (
              <MainCard
                title={selectedScreenshot.routeName}
                secondary={
                  <Box>
                    <Chip label={selectedScreenshot.viewportSize} size="small" sx={{ mr: 1 }} />
                    <Chip label={selectedScreenshot.role} size="small" />
                  </Box>
                }
              >
                <Typography variant="body2" color="textSecondary" gutterBottom>
                  {selectedScreenshot.path} (Captured: {new Date(selectedScreenshot.timestamp).toLocaleString()})
                </Typography>

                <Box position="relative" mt={2} mb={4} sx={{ border: `1px solid ${theme.palette.divider}` }}>
                  <CardMedia
                    component="img"
                    image={`/path/to/screenshot/${selectedScreenshot.screenshotPath}`}
                    alt={selectedScreenshot.routeName}
                    onClick={handleImageClick}
                    sx={{ cursor: 'crosshair', maxHeight: '600px', objectFit: 'contain' }}
                  />

                  {/* If no screenshots available, show a placeholder */}
                  <Typography
                    variant="body2"
                    color="textSecondary"
                    sx={{ position: 'absolute', top: '50%', left: '50%', transform: 'translate(-50%, -50%)' }}
                  >
                    This is a mock placeholder. In a real implementation, the actual screenshot would be displayed here.
                  </Typography>

                  {/* Show existing issues as pins */}
                  {issues
                    .filter(
                      (issue) => issue.routeName === selectedScreenshot.routeName && issue.viewportSize === selectedScreenshot.viewportSize
                    )
                    .map((issue) => (
                      <Tooltip key={issue.id} title={issue.description} arrow>
                        <Box
                          sx={{
                            position: 'absolute',
                            left: issue.coordinates.x - 12,
                            top: issue.coordinates.y - 12,
                            width: 24,
                            height: 24,
                            borderRadius: '50%',
                            bgcolor:
                              issue.status === 'open'
                                ? 'error.main'
                                : issue.status === 'in-progress'
                                  ? 'warning.main'
                                  : issue.status === 'fixed'
                                    ? 'success.main'
                                    : 'grey.500',
                            border: '2px solid white',
                            boxShadow: 2,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            color: 'white',
                            fontWeight: 'bold',
                            fontSize: '12px',
                            cursor: 'pointer',
                            zIndex: 2
                          }}
                        >
                          !
                        </Box>
                      </Tooltip>
                    ))}

                  {/* Show new issue marker if coordinates are set */}
                  {newIssue.coordinates && newIssue.coordinates.x > 0 && (
                    <Box
                      sx={{
                        position: 'absolute',
                        left: newIssue.coordinates.x - 12,
                        top: newIssue.coordinates.y - 12,
                        width: 24,
                        height: 24,
                        borderRadius: '50%',
                        bgcolor: 'primary.main',
                        border: '2px solid white',
                        boxShadow: 2,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: 'white',
                        fontWeight: 'bold',
                        fontSize: '12px',
                        zIndex: 2
                      }}
                    >
                      +
                    </Box>
                  )}
                </Box>

                <Box mt={4}>
                  <Typography variant="h6" gutterBottom>
                    Add UI Issue
                  </Typography>

                  <TextField
                    fullWidth
                    label="Issue Description"
                    multiline
                    rows={2}
                    value={newIssue.description}
                    onChange={handleDescriptionChange}
                    sx={{ mb: 2 }}
                    placeholder="Describe the UI issue (e.g., 'Button alignment is off', 'Text is cut off in mobile view')"
                  />

                  <Button
                    variant="contained"
                    color="primary"
                    onClick={handleCreateIssue}
                    disabled={!newIssue.description || newIssue.coordinates?.x === 0}
                  >
                    Create Issue
                  </Button>

                  <Typography variant="caption" display="block" sx={{ mt: 1, color: 'text.secondary' }}>
                    Click on the image to mark the location of the issue before creating
                  </Typography>
                </Box>

                <Divider sx={{ my: 4 }} />

                <Box>
                  <Typography variant="h6" gutterBottom>
                    Existing Issues
                  </Typography>

                  {issues
                    .filter(
                      (issue) => issue.routeName === selectedScreenshot.routeName && issue.viewportSize === selectedScreenshot.viewportSize
                    )
                    .map((issue) => (
                      <Paper key={issue.id} sx={{ p: 2, mb: 2 }}>
                        <Typography variant="subtitle2">{issue.description}</Typography>
                        <Box display="flex" alignItems="center" mt={1}>
                          <Chip
                            label={issue.status}
                            size="small"
                            color={
                              issue.status === 'open'
                                ? 'error'
                                : issue.status === 'in-progress'
                                  ? 'warning'
                                  : issue.status === 'fixed'
                                    ? 'success'
                                    : 'default'
                            }
                            sx={{ mr: 1 }}
                          />
                          <Typography variant="caption" color="textSecondary">
                            Created: {new Date(issue.createdAt).toLocaleDateString()}
                          </Typography>
                        </Box>
                      </Paper>
                    ))}

                  {issues.filter(
                    (issue) => issue.routeName === selectedScreenshot.routeName && issue.viewportSize === selectedScreenshot.viewportSize
                  ).length === 0 && (
                    <Typography variant="body2" color="textSecondary">
                      No issues reported for this screenshot
                    </Typography>
                  )}
                </Box>
              </MainCard>
            ) : (
              <MainCard>
                <Box display="flex" flexDirection="column" alignItems="center" justifyContent="center" sx={{ py: 10 }}>
                  <Typography variant="h6" align="center" gutterBottom>
                    Select a screenshot from the left panel to view and annotate
                  </Typography>
                  <Typography variant="body2" color="textSecondary" align="center">
                    Use this tool to identify and track UI cleanliness issues across different routes and viewport sizes
                  </Typography>
                </Box>
              </MainCard>
            )}
          </Grid>
        </Grid>
      )}
    </Container>
  );
};

export default ScreenshotViewerPage;
