import { lazy } from 'react';
import Loadable from '[components]/Loadable';
import ROUTES from 'Routing/appRoutes';

// Compliance Reports
const AllComplianceReports = Loadable(lazy(() => import('./[pages]/AllComplianceReports')));
const ViewComplianceReport = Loadable(lazy(() => import('./ViewComplianceReport')));
const EditComplianceReport = Loadable(lazy(() => import('./[pages]/EditComplianceReport')));
const ReviewComplianceReport = Loadable(lazy(() => import('./ReviewComplianceReport')));

const complianceReportRoutes = {
  path: 'compliance-reports',
  children: [
    { path: '', element: <AllComplianceReports /> },
    { path: ROUTES.COMPLIANCE_REPORTS.DETAILS_PATTERN, element: <ViewComplianceReport /> },
    { path: ROUTES.COMPLIANCE_REPORTS.EDIT_PATTERN, element: <EditComplianceReport /> },
    { path: ROUTES.COMPLIANCE_REPORTS.REVIEW_PATTERN, element: <ReviewComplianceReport /> }
  ]
};

export default complianceReportRoutes;
