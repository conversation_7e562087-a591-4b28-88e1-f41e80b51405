// Compliance Report service
// Handles operations for compliance reports

import { db as firestore } from '../../Firebase/[config]/firebase';
import { collection, doc, getDoc, addDoc, updateDoc, deleteDoc, query, where, getDocs, orderBy, limit } from 'firebase/firestore';
import { ComplianceReport } from '../[types]/ComplianceReport';
import { Timestamp, DocumentData, QueryDocumentSnapshot } from 'firebase/firestore';
import { UserID } from '../../Users/<USER>/User';

// Collection references
const reportsCollection = collection(firestore, 'compliance_reports');

/**
 * Convert Firestore document to ComplianceReport
 */
export const convertToComplianceReport = (doc: QueryDocumentSnapshot<DocumentData>): ComplianceReport => {
  const data = doc.data();

  return {
    id: doc.id,
    questionnaireId: data.questionnaireId || '',
    patientId: data.patientId || '',
    doctorId: data.doctorId || '',
    clientId: data.clientId || undefined,
    pdfUrl: data.pdfUrl || undefined,
    respiratorClearance: data.respiratorClearance || 0,
    respiratorClearanceDescription: data.respiratorClearanceDescription || '',
    workLoadLimitation: data.workLoadLimitation || 0,
    clearanceBasedOn: data.clearanceBasedOn || '',
    followUp: data.followUp || false,
    followUpDescription: data.followUpDescription || undefined,
    isAutomaticallyGenerated: data.isAutomaticallyGenerated || false,
    isSignedByDoctor: data.isSignedByDoctor || false,
    version: data.version || 1,
    status: data.status || 'draft',
    generationMethod: data.generationMethod || 'manual',
    signatureUrl: data.signatureUrl || undefined,
    signatureDate: data.signatureDate || undefined,
    medicalFindings: data.medicalFindings || [],
    createdAt: data.createdAt || Timestamp.now(),
    updatedAt: data.updatedAt || Timestamp.now()
  };
};

/**
 * Get a compliance report by ID
 */
export const getReportById = async (id: string): Promise<ComplianceReport | null> => {
  const reportRef = doc(reportsCollection, id);
  const reportSnap = await getDoc(reportRef);
  if (!reportSnap.exists()) return null;
  return convertToComplianceReport(reportSnap as QueryDocumentSnapshot<DocumentData>);
};

/**
 * Get compliance reports with filtering options
 */
export const getReports = async (
  options: {
    clinicId?: string;
    doctorId?: UserID;
    clientId?: UserID;
    status?: string;
    reportType?: string;
    period?: string;
    limit?: number;
    orderByField?: string;
    orderDirection?: 'asc' | 'desc';
  } = {}
): Promise<ComplianceReport[]> => {
  try {
    let q = query(reportsCollection);

    // Apply filters
    if (options.clinicId) {
      q = query(q, where('clinicId', '==', options.clinicId));
    }

    if (options.doctorId) {
      q = query(q, where('doctorId', '==', options.doctorId));
    }

    if (options.clientId) {
      q = query(q, where('clientId', '==', options.clientId));
    }

    if (options.status) {
      q = query(q, where('status', '==', options.status));
    }

    if (options.reportType) {
      q = query(q, where('reportType', '==', options.reportType));
    }

    if (options.period) {
      q = query(q, where('period', '==', options.period));
    }

    // Apply sorting
    if (options.orderByField) {
      q = query(q, orderBy(options.orderByField, options.orderDirection || 'desc'));
    } else {
      q = query(q, orderBy('createdAt', 'desc'));
    }

    // Apply limit
    if (options.limit) {
      q = query(q, limit(options.limit));
    }

    const querySnap = await getDocs(q);
    return querySnap.docs.map((doc: any) => convertToComplianceReport(doc));
  } catch (error) {
    console.error('Error getting reports:', error);
    throw error;
  }
};

/**
 * Create a new compliance report
 */
export const createReport = async (data: Omit<ComplianceReport, 'id'>): Promise<ComplianceReport> => {
  try {
    const now = Timestamp.now();
    const reportData = {
      ...data,
      createdAt: now,
      updatedAt: now
    };

    const docRef = await addDoc(reportsCollection, reportData);
    const newReport = await getDoc(docRef);
    return convertToComplianceReport(newReport as QueryDocumentSnapshot<DocumentData>);
  } catch (error) {
    console.error('Error creating report:', error);
    throw error;
  }
};

/**
 * Update an existing compliance report
 */
export const updateReport = async (id: string, data: Partial<ComplianceReport>): Promise<ComplianceReport> => {
  try {
    const reportRef = doc(reportsCollection, id);
    const reportDoc = await getDoc(reportRef);

    if (!reportDoc.exists()) {
      throw new Error(`Report with ID ${id} not found`);
    }

    const now = Timestamp.now();

    // Create a copy of data without the id field
    const dataWithoutId = { ...data };
    delete dataWithoutId.id; // Remove the id property if present

    await updateDoc(reportRef, {
      ...dataWithoutId,
      updatedAt: now
    });

    const updatedReport = await getDoc(reportRef);
    return convertToComplianceReport(updatedReport as QueryDocumentSnapshot<DocumentData>);
  } catch (error) {
    console.error('Error updating report:', error);
    throw error;
  }
};

/**
 * Delete a compliance report
 */
export const deleteReport = async (id: string): Promise<void> => {
  try {
    const reportRef = doc(reportsCollection, id);
    await deleteDoc(reportRef);
  } catch (error) {
    console.error('Error deleting report:', error);
    throw error;
  }
};

/**
 * Submit a compliance report
 */
export const submitReport = async (id: string): Promise<ComplianceReport> => {
  try {
    const reportRef = doc(reportsCollection, id);
    const reportDoc = await getDoc(reportRef);

    if (!reportDoc.exists()) {
      throw new Error(`Report with ID ${id} not found`);
    }

    const now = Timestamp.now();

    await updateDoc(reportRef, {
      status: 'submitted',
      submittedAt: now,
      updatedAt: now
    });

    const updatedReport = await getDoc(reportRef);
    return convertToComplianceReport(updatedReport as QueryDocumentSnapshot<DocumentData>);
  } catch (error) {
    console.error('Error submitting report:', error);
    throw error;
  }
};

/**
 * Review a compliance report
 */
export const reviewReport = async (id: string, reviewedBy: UserID): Promise<ComplianceReport> => {
  try {
    const reportRef = doc(reportsCollection, id);
    const reportDoc = await getDoc(reportRef);

    if (!reportDoc.exists()) {
      throw new Error(`Report with ID ${id} not found`);
    }

    const now = Timestamp.now();

    await updateDoc(reportRef, {
      status: 'reviewed',
      reviewedAt: now,
      reviewedBy,
      updatedAt: now
    });

    const updatedReport = await getDoc(reportRef);
    return convertToComplianceReport(updatedReport as QueryDocumentSnapshot<DocumentData>);
  } catch (error) {
    console.error('Error reviewing report:', error);
    throw error;
  }
};
