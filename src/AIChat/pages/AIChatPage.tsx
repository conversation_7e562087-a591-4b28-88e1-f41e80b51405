import React, { useState, useRef, useEffect } from 'react';
import {
  Box,
  Typography,
  TextField,
  Button,
  Paper,
  Avatar,
  Container,
  Divider,
  Grid,
  CircularProgress,
  IconButton,
  useTheme
} from '@mui/material';
import { styled } from '@mui/material/styles';
import SendIcon from '@mui/icons-material/Send';
import SmartToyIcon from '@mui/icons-material/SmartToy';
import PersonIcon from '@mui/icons-material/Person';
import DeleteIcon from '@mui/icons-material/Delete';
import MainCard from '../../Chat/components/MainCard';
import { useAuth } from 'Authentication/[contexts]/AuthContext';

// Define message type
interface Message {
  id: string;
  text: string;
  sender: 'user' | 'ai';
  timestamp: Date;
}

// Styled components for chat UI
const MessageList = styled(Box)(({ theme }) => ({
  flex: '1 1 auto',
  overflowY: 'auto',
  padding: theme.spacing(2),
  maxHeight: 'calc(100vh - 300px)',
  minHeight: '400px'
}));

const MessageItem = styled(Box, {
  shouldForwardProp: (prop) => prop !== 'isUser'
})<{ isUser?: boolean }>(({ theme, isUser }) => ({
  display: 'flex',
  marginBottom: theme.spacing(2),
  flexDirection: isUser ? 'row-reverse' : 'row'
}));

const MessageBubble = styled(Paper, {
  shouldForwardProp: (prop) => prop !== 'isUser'
})<{ isUser?: boolean }>(({ theme, isUser }) => ({
  padding: theme.spacing(1.5),
  maxWidth: '80%',
  borderRadius: 12,
  wordBreak: 'break-word',
  marginLeft: isUser ? 0 : theme.spacing(1),
  marginRight: isUser ? theme.spacing(1) : 0,
  backgroundColor: isUser ? theme.palette.primary.main : theme.palette.background.paper,
  color: isUser ? theme.palette.primary.contrastText : theme.palette.text.primary,
  boxShadow: theme.shadows[1]
}));

const TimeStamp = styled(Typography)(({ theme }) => ({
  fontSize: '0.7rem',
  marginTop: theme.spacing(0.5),
  color: theme.palette.text.secondary,
  textAlign: 'right'
}));

const AIChatPage: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const messageListRef = useRef<HTMLDivElement>(null);
  const { userData } = useAuth();
  const theme = useTheme();

  // When component mounts, add a welcome message
  useEffect(() => {
    const welcomeMessage: Message = {
      id: Date.now().toString(),
      text: `Hello${userData?.firstName ? ` ${userData.firstName}` : ''}! I'm your AI assistant. How can I help you today?`,
      sender: 'ai',
      timestamp: new Date()
    };
    setMessages([welcomeMessage]);
  }, [userData]);

  // Scroll to bottom when messages change
  useEffect(() => {
    if (messageListRef.current) {
      messageListRef.current.scrollTop = messageListRef.current.scrollHeight;
    }
  }, [messages]);

  // Format time for messages
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // Generate AI response based on user input
  const generateAIResponse = (userMessage: string): Promise<string> => {
    return new Promise((resolve) => {
      // Simulate AI thinking time
      setTimeout(
        () => {
          // Simple rule-based responses
          if (userMessage.toLowerCase().includes('hello') || userMessage.toLowerCase().includes('hi')) {
            resolve('Hello! How can I assist you today?');
          } else if (userMessage.toLowerCase().includes('how are you')) {
            resolve("I'm just a program, but I'm functioning well! How can I help you?");
          } else if (userMessage.toLowerCase().includes('bye') || userMessage.toLowerCase().includes('goodbye')) {
            resolve('Goodbye! Feel free to come back if you have more questions.');
          } else if (userMessage.toLowerCase().includes('thank')) {
            resolve("You're welcome! Let me know if you need anything else.");
          } else if (userMessage.toLowerCase().includes('help')) {
            resolve('I can answer questions, provide information, or just chat. What would you like to know?');
          } else if (userMessage.toLowerCase().includes('name')) {
            resolve("I'm your AI assistant. You can call me Assistant!");
          } else if (userMessage.toLowerCase().includes('weather')) {
            resolve("I can't access real-time weather data, but you might want to check a weather app or website for that information.");
          } else if (userMessage.toLowerCase().includes('joke')) {
            const jokes = [
              "Why don't scientists trust atoms? Because they make up everything!",
              'Why did the scarecrow win an award? Because he was outstanding in his field!',
              'I told my wife she was drawing her eyebrows too high. She looked surprised.',
              'What do you call a fake noodle? An impasta!',
              'How do you organize a space party? You planet!'
            ];
            resolve(jokes[Math.floor(Math.random() * jokes.length)]);
          } else {
            resolve("That's an interesting point. Could you tell me more about what you're looking for?");
          }
        },
        1000 + Math.random() * 1000
      ); // Random delay between 1-2 seconds
    });
  };

  // Handle sending a new message
  const handleSendMessage = async () => {
    if (!newMessage.trim()) return;

    // Add user message
    const userMessage: Message = {
      id: Date.now().toString(),
      text: newMessage,
      sender: 'user',
      timestamp: new Date()
    };

    setMessages((prevMessages) => [...prevMessages, userMessage]);
    setNewMessage('');
    setIsTyping(true);

    try {
      // Get AI response
      const aiResponse = await generateAIResponse(newMessage);

      // Add AI message
      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: aiResponse,
        sender: 'ai',
        timestamp: new Date()
      };

      setMessages((prevMessages) => [...prevMessages, aiMessage]);
    } catch (error) {
      console.error('Error generating AI response:', error);

      // Add error message if AI response fails
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: "I'm sorry, I had trouble processing that. Could you try again?",
        sender: 'ai',
        timestamp: new Date()
      };

      setMessages((prevMessages) => [...prevMessages, errorMessage]);
    } finally {
      setIsTyping(false);
    }
  };

  // Handle pressing Enter key to send message
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Clear all messages
  const handleClearChat = () => {
    setMessages([]);

    // Add a new welcome message
    setTimeout(() => {
      const welcomeMessage: Message = {
        id: Date.now().toString(),
        text: `Let's start a new conversation. How can I help you today?`,
        sender: 'ai',
        timestamp: new Date()
      };
      setMessages([welcomeMessage]);
    }, 300);
  };

  return (
    <Container maxWidth="lg" sx={{ mt: 2, mb: 4 }}>
      <MainCard
        title={
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Avatar
                sx={{
                  bgcolor: theme.palette.primary.main,
                  color: 'white',
                  mr: 1
                }}
              >
                <SmartToyIcon />
              </Avatar>
              <Typography variant="h3">AI Chat Assistant</Typography>
            </Box>
            <IconButton color="error" onClick={handleClearChat} size="small" title="Clear chat">
              <DeleteIcon />
            </IconButton>
          </Box>
        }
      >
        <Grid container spacing={2}>
          <Grid item xs={12}>
            {/* Message List */}
            <MessageList ref={messageListRef}>
              {messages.length === 0 ? (
                <Box sx={{ textAlign: 'center', py: 5 }}>
                  <Typography color="textSecondary">No messages yet. Start the conversation!</Typography>
                </Box>
              ) : (
                messages.map((msg) => (
                  <MessageItem key={msg.id} isUser={msg.sender === 'user'}>
                    <Avatar
                      sx={{
                        width: 36,
                        height: 36,
                        bgcolor: msg.sender === 'ai' ? 'primary.main' : 'secondary.main'
                      }}
                    >
                      {msg.sender === 'ai' ? <SmartToyIcon /> : <PersonIcon />}
                    </Avatar>
                    <Box sx={{ maxWidth: '80%' }}>
                      <MessageBubble isUser={msg.sender === 'user'}>
                        <Typography variant="body1" component="div">
                          {msg.text.split('\n').map((text, i) => (
                            <React.Fragment key={i}>
                              {text}
                              {i !== msg.text.split('\n').length - 1 && <br />}
                            </React.Fragment>
                          ))}
                        </Typography>
                      </MessageBubble>
                      <TimeStamp>{formatTime(msg.timestamp)}</TimeStamp>
                    </Box>
                  </MessageItem>
                ))
              )}

              {isTyping && (
                <MessageItem>
                  <Avatar sx={{ width: 36, height: 36, bgcolor: 'primary.main' }}>
                    <SmartToyIcon />
                  </Avatar>
                  <Box sx={{ maxWidth: '80%' }}>
                    <MessageBubble>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Typography variant="body2" color="textSecondary" sx={{ mr: 1 }}>
                          Typing
                        </Typography>
                        <CircularProgress size={16} />
                      </Box>
                    </MessageBubble>
                  </Box>
                </MessageItem>
              )}
            </MessageList>

            <Divider sx={{ my: 2 }} />

            {/* Message Input */}
            <Box sx={{ display: 'flex' }}>
              <TextField
                fullWidth
                placeholder="Type your message..."
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                onKeyDown={handleKeyPress}
                variant="outlined"
                multiline
                maxRows={4}
                sx={{ mr: 1 }}
              />
              <Button
                variant="contained"
                color="primary"
                endIcon={<SendIcon />}
                onClick={handleSendMessage}
                disabled={!newMessage.trim() || isTyping}
                sx={{ alignSelf: 'flex-end', height: 56 }}
              >
                Send
              </Button>
            </Box>
          </Grid>
        </Grid>
      </MainCard>
    </Container>
  );
};

export default AIChatPage;
