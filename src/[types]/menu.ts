// ==============================|| MENU TYPES ||============================== //
import { ReactNode } from 'react';

export interface MenuProps {
  /**
   * Indicate if dashboard layout menu open or not
   */
  isDashboardDrawerOpened: boolean;
}

export type NavItemType = {
  id: string;
  title: string;
  // Standardize type field to include all possible types used in the application
  type: 'item' | 'group' | 'collapse';
  url?: string;
  icon?:
    | ReactNode
    | {
        type: string;
        value: string;
      };
  link?: string;
  children?: NavItemType[];
  breadcrumbs?: boolean;
  external?: boolean;
  target?: string;
  disabled?: boolean;
  caption?: string;
  chip?: {
    color: 'default' | 'primary' | 'secondary' | 'error' | 'warning' | 'info' | 'success';
    variant: 'filled' | 'outlined';
    size: 'small' | 'medium';
    label: string;
    avatar?: string;
  };
  elements?: NavItemType[];
  // Add RBAC properties used elsewhere
  rbac?:
    | {
        resource: string;
        action: string;
      }
    | string[];
};

export type MenuItem = {
  id: string;
  title: string;
  type: 'item' | 'group';
  url?: string;
  icon?:
    | ReactNode
    | {
        type: string;
        value: string;
      };
  link?: string;
  children?: MenuItem[];
  breadcrumbs?: boolean;
  external?: boolean;
  target?: string;
  disabled?: boolean;
  caption?: string;
  chip?: {
    color: 'default' | 'primary' | 'secondary' | 'error' | 'warning' | 'info' | 'success';
    variant: 'filled' | 'outlined';
    size: 'small' | 'medium';
    label: string;
    avatar?: string;
  };
  elements?: MenuItem[];
};
