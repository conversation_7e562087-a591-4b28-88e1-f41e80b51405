export enum ActivityType {
  PAGE_VIEW = 'PAGE_VIEW',
  BUTTON_CLICK = 'BUTTON_CLICK',
  FORM_SUBMIT = 'FORM_SUBMIT',
  DATA_CHANGE = 'DATA_CHANGE',
  AUTHENTICATION = 'AUTHENTICATION',
  NOTIFICATION = 'NOTIFICATION',
  RESOURCE_ACCESS = 'RESOURCE_ACCESS',
  SYSTEM_EVENT = 'SYSTEM_EVENT',
  CUSTOM = 'CUSTOM'
}

export enum ActivityStatus {
  STARTED = 'STARTED',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED'
}

export interface Activity {
  id?: string;
  timestamp: Date;
  userId?: string;
  userName?: string;
  type: ActivityType;
  description: string;
  status: ActivityStatus;
  resourceId?: string;
  resourceType?: string;
  location?: string; // URL or route path
  metadata?: Record<string, any>;
  duration?: number; // in milliseconds
  relatedActivityId?: string; // for tracking related activities
}

export interface ActivityFilter {
  userId?: string;
  types?: ActivityType[];
  resourceType?: string;
  resourceId?: string;
  startDate?: Date;
  endDate?: Date;
  status?: ActivityStatus;
}

export interface ActivityOptions {
  shouldNotify?: boolean; // whether to create a notification
  notificationTitle?: string;
  notificationMessage?: string;
  isHighPriority?: boolean;
  expiresAt?: Date;
}
