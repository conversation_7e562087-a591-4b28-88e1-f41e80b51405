// material-ui
import { Theme, SxProps } from '@mui/material/styles';

// project imports
import { DropzopType } from '../[constants]/config';

// third party
import { DropzoneOptions } from 'react-dropzone';

// ==============================|| TYPES - DROPZONE ||============================== //

export interface CustomFile extends File {
  path?: string;
  preview?: string;
  lastModifiedDate?: Date;
}

export interface UploadProps {
  error: boolean;
  file?: CustomFile | CustomFile[];
  setFieldValue?: (name: string, value: any) => void;
  preview?: string;
  onDrop?: (acceptedFiles: CustomFile[]) => void;
  onRemove?: (file: CustomFile | string) => void;
  onRemoveAll?: () => void;
  onUpload?: () => void;
  fileLimit?: number;
  multiple?: boolean;
  showList?: boolean;
  showActionButtons?: boolean;
  accept?: DropzoneOptions['accept'];
  maxSize?: number;
  sx?: SxProps<Theme>;
  type?: (typeof DropzopType)[keyof typeof DropzopType];
}

export interface FilePreviewProps {
  file?: CustomFile;
  files?: CustomFile[];
  showList?: boolean;
  onRemove?: (file: CustomFile | string) => void;
  type?: (typeof DropzopType)[keyof typeof DropzopType];
}

export interface SingleFileProps {
  file: CustomFile;
  error: boolean;
  sx?: SxProps<Theme>;
}

export interface MultiFileProps {
  files: CustomFile[];
  error: boolean;
  showList?: boolean;
  onRemove: (file: CustomFile | string) => void;
  onRemoveAll: () => void;
  sx?: SxProps<Theme>;
  type?: (typeof DropzopType)[keyof typeof DropzopType];
}

export interface UploadMultiFileProps extends MultiFileProps {
  setFieldValue?: (name: string, value: any) => void;
  type?: (typeof DropzopType)[keyof typeof DropzopType];
  onUpload?: () => void;
}
