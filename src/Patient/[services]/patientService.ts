// Patient service
// Handles operations specific to patients

import { db as firestore } from '../../Firebase/[config]/firebase';
import { collection, doc, getDoc, updateDoc, query, where, getDocs } from 'firebase/firestore';
import { TRQUser, UserID } from '../../Users/<USER>/User';
import { Role } from 'RBAC/[types]/Role';
import { getUsers, updateUser } from '../../Users/<USER>/userService';

const usersCollection = collection(firestore, 'users');

/**
 * Get all patients with optional filtering
 */
export const getAllPatients = async (
  options: {
    clinicId?: string;
    doctorId?: UserID;
    clientId?: UserID;
    isActive?: boolean;
    limit?: number;
    orderByField?: string;
    orderDirection?: 'asc' | 'desc';
  } = {}
): Promise<TRQUser[]> => {
  return getUsers({
    role: Role.Patient,
    ...options
  });
};

/**
 * Get patient by ID
 */
export const getPatientById = async (patientId: UserID): Promise<TRQUser | null> => {
  const patientRef = doc(usersCollection, patientId);
  const patientSnap = await getDoc(patientRef);
  if (!patientSnap.exists()) return null;
  return { id: patientSnap.id, ...patientSnap.data() } as TRQUser;
};

/**
 * Assign a doctor to a patient
 */
export const assignDoctorToPatient = async (patientId: UserID, doctorId: UserID): Promise<TRQUser> => {
  const patientRef = doc(usersCollection, patientId);
  await updateDoc(patientRef, { asPatient: { doctorId } });
  const updatedSnap = await getDoc(patientRef);
  return { id: updatedSnap.id, ...updatedSnap.data() } as TRQUser;
};

/**
 * Assign a patient to a clinic
 */
export const assignPatientToClinic = async (patientId: UserID, clinicId: string): Promise<TRQUser> => {
  const patientRef = doc(usersCollection, patientId);
  await updateDoc(patientRef, { asPatient: { clinicId } });
  const updatedSnap = await getDoc(patientRef);
  return { id: updatedSnap.id, ...updatedSnap.data() } as TRQUser;
};

/**
 * Assign a patient to a client
 */
export const assignPatientToClient = async (patientId: UserID, clientId: UserID): Promise<TRQUser> => {
  const patientRef = doc(usersCollection, patientId);
  await updateDoc(patientRef, { asPatient: { clientId } });
  const updatedSnap = await getDoc(patientRef);
  return { id: updatedSnap.id, ...updatedSnap.data() } as TRQUser;
};

/**
 * Get patients by field
 */
export const getPatientsByField = async (field: string, value: any): Promise<TRQUser[]> => {
  const q = query(usersCollection, where(field, '==', value));
  const querySnap = await getDocs(q);
  return querySnap.docs.map((doc) => ({ id: doc.id, ...doc.data() }) as TRQUser);
};
