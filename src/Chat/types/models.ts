import { Timestamp } from 'firebase/firestore';

// User model for Chat
export interface ChatUser {
  id: string; // Firebase UID
  name: string;
  email: string;
  avatar?: string;
  online_status?: 'available' | 'do_not_disturb' | 'offline';
  last_active: Timestamp;
}

// Conversation model
export interface Conversation {
  id: string; // Auto-generated
  participants: string[]; // User IDs
  created_at: Timestamp;
  updated_at: Timestamp;
  type: 'direct' | 'group' | 'automated';
  title?: string; // For group chats
}

// Message model
export interface Message {
  id: string; // Auto-generated
  conversation_id: string;
  sender_id: string;
  text: string;
  created_at: Timestamp;
  is_automated?: boolean;
  message_type: 'text' | 'image' | 'file';
  file_url?: string; // Optional for attachments
}

// User conversation mapping
export interface UserConversation {
  user_id: string;
  conversation_id: string;
  last_read: Timestamp;
  is_muted: boolean;
}
