import React, { Suspense, ReactElement } from 'react';

// Material UI
import { LinearProgress } from '@mui/material';
import { styled } from '@mui/material/styles';

// Styled component for LinearProgress
const LoaderWrapper = styled('div')({
  position: 'fixed',
  top: 0,
  left: 0,
  zIndex: 1301,
  width: '100%'
});

// ==============================|| LOADABLE - LAZY LOADING COMPONENT ||============================== //

/**
 * A higher-order component that enables code-splitting by wrapping a React lazy component
 * with a Suspense fallback.
 */
const Loadable =
  (Component: React.ComponentType<any>) =>
  (props: any): ReactElement => (
    <Suspense
      fallback={
        <LoaderWrapper>
          <LinearProgress color="primary" />
        </LoaderWrapper>
      }
    >
      <Component {...props} />
    </Suspense>
  );

export default Loadable;
