import React, { useState, useEffect, useRef } from 'react';
import { Box, Typography, TextField, Button, Paper, Avatar, Grid, CircularProgress } from '@mui/material';
import { styled } from '@mui/material/styles';
import SendIcon from '@mui/icons-material/Send';
import PersonIcon from '@mui/icons-material/Person';

// Firebase imports
import { db } from '../Firebase/[config]/firebase';
import {
  collection,
  doc,
  getDoc,
  addDoc,
  setDoc,
  query,
  orderBy,
  onSnapshot,
  serverTimestamp,
  DocumentData,
  QueryDocumentSnapshot,
  Timestamp
} from 'firebase/firestore';
import MainCard from './components/MainCard';

// Message type
interface Message {
  id: string;
  text: string;
  sender: string;
  sender_name: string;
  timestamp: Timestamp | null;
}

// Styled components
const MessageList = styled(Box)(({ theme }) => ({
  flex: '1 1 auto',
  overflowY: 'auto',
  padding: theme.spacing(2),
  maxHeight: 'calc(100vh - 300px)',
  minHeight: '300px'
}));

const MessageItem = styled(Box, {
  shouldForwardProp: (prop) => prop !== 'isCurrentUser'
})<{ isCurrentUser?: boolean }>(({ theme, isCurrentUser }) => ({
  display: 'flex',
  marginBottom: theme.spacing(2),
  flexDirection: isCurrentUser ? 'row-reverse' : 'row'
}));

const MessageBubble = styled(Paper, {
  shouldForwardProp: (prop) => prop !== 'isCurrentUser'
})<{ isCurrentUser?: boolean }>(({ theme, isCurrentUser }) => ({
  padding: theme.spacing(1.5),
  maxWidth: '70%',
  borderRadius: 12,
  wordBreak: 'break-word',
  marginLeft: isCurrentUser ? 0 : theme.spacing(1),
  marginRight: isCurrentUser ? theme.spacing(1) : 0,
  backgroundColor: isCurrentUser ? theme.palette.primary.main : theme.palette.background.paper,
  color: isCurrentUser ? theme.palette.primary.contrastText : theme.palette.text.primary
}));

const TimeStamp = styled(Typography)(({ theme }) => ({
  fontSize: '0.7rem',
  marginTop: theme.spacing(0.5),
  color: theme.palette.text.secondary,
  textAlign: 'right'
}));

// Main component
const SimpleChatDemo: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(true);
  const messageListRef = useRef<HTMLDivElement>(null);

  // Mock current user - in production, this would come from auth
  const currentUser = {
    id: 'user1',
    name: 'Demo User'
  };

  // Chat room ID - in production, this would be dynamic
  const roomId = 'demo-chat-room';

  // Subscribe to messages
  useEffect(() => {
    setLoading(true);

    // Create reference to messages collection
    const messagesRef = collection(db, 'chat_rooms', roomId, 'messages');

    // Query to get messages ordered by timestamp
    const messagesQuery = query(messagesRef, orderBy('timestamp', 'asc'));

    // Set up real-time listener
    const unsubscribe = onSnapshot(messagesQuery, (snapshot) => {
      const fetchedMessages: Message[] = snapshot.docs.map(
        (doc: QueryDocumentSnapshot<DocumentData>) =>
          ({
            id: doc.id,
            ...doc.data()
          }) as Message
      );

      setMessages(fetchedMessages);
      setLoading(false);

      // Scroll to bottom after messages load
      setTimeout(() => {
        if (messageListRef.current) {
          messageListRef.current.scrollTop = messageListRef.current.scrollHeight;
        }
      }, 100);
    });

    // Create the demo room document if it doesn't exist
    const initializeRoom = async () => {
      // Check if room exists
      const roomRef = doc(db, 'chat_rooms', roomId);
      const roomDoc = await getDoc(roomRef);

      if (!roomDoc.exists()) {
        // Create the room document
        await setDoc(roomRef, {
          created_at: serverTimestamp(),
          name: 'Demo Chat Room'
        });
      }

      // Send welcome message if no messages exist
      if (messages.length === 0) {
        await addDoc(messagesRef, {
          text: 'Welcome to the chat room! This is a system message.',
          sender: 'system',
          sender_name: 'System',
          timestamp: serverTimestamp()
        });
      }
    };

    initializeRoom();

    // Clean up listener on unmount
    return () => unsubscribe();
  }, [roomId, messages.length]);

  // Handle send message
  const handleSendMessage = async () => {
    if (newMessage.trim() === '') return;

    // Reference to messages collection
    const messagesRef = collection(db, 'chat_rooms', roomId, 'messages');

    // Add new message
    await addDoc(messagesRef, {
      text: newMessage,
      sender: currentUser.id,
      sender_name: currentUser.name,
      timestamp: serverTimestamp()
    });

    setNewMessage('');
  };

  // Handle key press (Enter to send)
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Format timestamp
  const formatTime = (timestamp: Timestamp | null) => {
    if (!timestamp) return '';

    const date = timestamp.toDate ? timestamp.toDate() : new Date();
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <MainCard title="Real-time Chat Demo">
      <Grid container spacing={2}>
        <Grid item xs={12}>
          {/* Message List */}
          <MessageList ref={messageListRef}>
            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                <CircularProgress />
              </Box>
            ) : messages.length === 0 ? (
              <Box sx={{ textAlign: 'center', py: 5 }}>
                <Typography color="textSecondary">No messages yet. Start the conversation!</Typography>
              </Box>
            ) : (
              messages.map((msg) => (
                <MessageItem key={msg.id} isCurrentUser={msg.sender === currentUser.id}>
                  <Avatar sx={{ width: 32, height: 32 }}>{msg.sender === 'system' ? 'S' : <PersonIcon />}</Avatar>
                  <Box sx={{ maxWidth: '80%' }}>
                    <MessageBubble isCurrentUser={msg.sender === currentUser.id}>
                      <Typography variant="subtitle2">{msg.sender_name}</Typography>
                      <Typography variant="body1">{msg.text}</Typography>
                    </MessageBubble>
                    <TimeStamp>{formatTime(msg.timestamp)}</TimeStamp>
                  </Box>
                </MessageItem>
              ))
            )}
          </MessageList>

          {/* Message Input */}
          <Box sx={{ mt: 2, display: 'flex' }}>
            <TextField
              fullWidth
              placeholder="Type a message"
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              variant="outlined"
              size="small"
            />
            <Button
              variant="contained"
              color="primary"
              endIcon={<SendIcon />}
              onClick={handleSendMessage}
              disabled={!newMessage.trim()}
              sx={{ ml: 1 }}
            >
              Send
            </Button>
          </Box>
        </Grid>
      </Grid>
    </MainCard>
  );
};

export default SimpleChatDemo;
