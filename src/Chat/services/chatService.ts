import {
  findOrCreateDirectChat,
  getUserConversations,
  getConversationMessages,
  sendMessage as firebaseSendMessage,
  sendAutomatedMessage,
  subscribeToConversations,
  subscribeToMessages,
  markConversationAsRead,
  createConversation,
  getUser as getUserData,
  getAllUsers,
  updateUserStatus
} from './chatFirestore';
import { Conversation, Message, ChatUser } from '../types/models';

// ==============================|| CHAT SERVICE ||============================== //

class ChatService {
  // Current user
  private currentUserId: string | null = null;

  // Subscription cleanup functions
  private conversationsUnsubscribe: (() => void) | null = null;
  private messageUnsubscribes: Map<string, () => void> = new Map();

  /**
   * Set the current user ID for the chat service
   */
  setCurrentUser(userId: string): void {
    this.currentUserId = userId;
  }

  /**
   * Clear the current user ID and unsubscribe from all listeners
   */
  clearCurrentUser(): void {
    this.currentUserId = null;
    this.unsubscribeFromAllListeners();
  }

  /**
   * Unsubscribe from all Firestore listeners
   */
  unsubscribeFromAllListeners(): void {
    if (this.conversationsUnsubscribe) {
      this.conversationsUnsubscribe();
      this.conversationsUnsubscribe = null;
    }

    this.messageUnsubscribes.forEach((unsubscribe) => unsubscribe());
    this.messageUnsubscribes.clear();
  }

  /**
   * Get current user data
   */
  async getCurrentUser(): Promise<ChatUser | null> {
    if (!this.currentUserId) {
      throw new Error('No current user set. Call setCurrentUser() first.');
    }

    return getUserData(this.currentUserId);
  }

  /**
   * Get user by ID
   */
  async getUser(userId: string): Promise<ChatUser | null> {
    return getUserData(userId);
  }

  /**
   * Get all users
   */
  async getUsers(): Promise<ChatUser[]> {
    return getAllUsers();
  }

  /**
   * Update the current user's online status
   */
  async updateStatus(status: 'available' | 'do_not_disturb' | 'offline'): Promise<void> {
    if (!this.currentUserId) {
      throw new Error('No current user set. Call setCurrentUser() first.');
    }

    return updateUserStatus(this.currentUserId, status);
  }

  /**
   * Get all conversations for the current user
   */
  async getConversations(): Promise<Conversation[]> {
    if (!this.currentUserId) {
      throw new Error('No current user set. Call setCurrentUser() first.');
    }

    return getUserConversations(this.currentUserId);
  }

  /**
   * Subscribe to conversations for the current user
   */
  subscribeToConversations(callback: (conversations: Conversation[]) => void): () => void {
    if (!this.currentUserId) {
      throw new Error('No current user set. Call setCurrentUser() first.');
    }

    // Unsubscribe from any existing conversation listener
    if (this.conversationsUnsubscribe) {
      this.conversationsUnsubscribe();
    }

    this.conversationsUnsubscribe = subscribeToConversations(this.currentUserId, callback);
    return this.conversationsUnsubscribe;
  }

  /**
   * Get messages for a specific conversation
   */
  async getMessages(conversationId: string, limit = 50): Promise<Message[]> {
    return getConversationMessages(conversationId, limit);
  }

  /**
   * Subscribe to messages for a specific conversation
   */
  subscribeToMessages(conversationId: string, callback: (messages: Message[]) => void, limit = 50): () => void {
    // Unsubscribe from any existing message listener for this conversation
    if (this.messageUnsubscribes.has(conversationId)) {
      const unsubscribe = this.messageUnsubscribes.get(conversationId)!;
      unsubscribe();
      this.messageUnsubscribes.delete(conversationId);
    }

    const unsubscribe = subscribeToMessages(conversationId, callback, limit);
    this.messageUnsubscribes.set(conversationId, unsubscribe);

    return () => {
      if (this.messageUnsubscribes.has(conversationId)) {
        const unsubscribe = this.messageUnsubscribes.get(conversationId)!;
        unsubscribe();
        this.messageUnsubscribes.delete(conversationId);
      }
    };
  }

  /**
   * Send a message to a conversation
   */
  async sendMessage(
    conversationId: string,
    text: string,
    messageType: 'text' | 'image' | 'file' = 'text',
    fileUrl?: string
  ): Promise<string> {
    if (!this.currentUserId) {
      throw new Error('No current user set. Call setCurrentUser() first.');
    }

    return firebaseSendMessage({
      conversation_id: conversationId,
      sender_id: this.currentUserId,
      text,
      message_type: messageType,
      file_url: fileUrl
    });
  }

  /**
   * Find or create a direct chat with another user
   */
  async findOrCreateDirectChat(otherUserId: string): Promise<string> {
    if (!this.currentUserId) {
      throw new Error('No current user set. Call setCurrentUser() first.');
    }

    return findOrCreateDirectChat(this.currentUserId, otherUserId);
  }

  /**
   * Mark a conversation as read
   */
  async markConversationAsRead(conversationId: string): Promise<void> {
    if (!this.currentUserId) {
      throw new Error('No current user set. Call setCurrentUser() first.');
    }

    return markConversationAsRead(this.currentUserId, conversationId);
  }

  /**
   * Create a new group conversation
   */
  async createGroupConversation(participants: string[], title: string): Promise<string> {
    if (!this.currentUserId) {
      throw new Error('No current user set. Call setCurrentUser() first.');
    }

    // Make sure the current user is included in participants
    if (!participants.includes(this.currentUserId)) {
      participants.push(this.currentUserId);
    }

    return createConversation({
      participants,
      type: 'group',
      title
    });
  }

  /**
   * Send an automated message to a conversation
   */
  static async sendAutomatedMessage(
    conversationId: string,
    text: string,
    messageType: 'text' | 'image' | 'file' = 'text',
    fileUrl?: string
  ): Promise<string> {
    return sendAutomatedMessage(conversationId, text, messageType, fileUrl);
  }

  /**
   * Send a welcome message when a user joins a conversation
   */
  static async sendWelcomeMessage(conversationId: string, userName: string): Promise<string> {
    return sendAutomatedMessage(conversationId, `Welcome, ${userName}! How can we help you today?`);
  }

  /**
   * Send an automated response after a period of inactivity
   */
  static async sendInactivityResponse(conversationId: string): Promise<string> {
    return sendAutomatedMessage(conversationId, "I noticed you've been inactive for a while. Is there anything else I can help you with?");
  }
}

// Create a singleton instance
const chatService = new ChatService();

export default chatService;
