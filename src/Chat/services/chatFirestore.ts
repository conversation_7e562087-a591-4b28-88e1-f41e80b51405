import firebase from '../../Firebase/[config]/firebase';
import { db as firestore } from '../../Firebase/[config]/firebase';
import { ChatUser, Conversation, Message } from '../types/models';

// Collection references
const usersRef = firestore.collection('users');
const conversationsRef = firestore.collection('conversations');
const messagesRef = firestore.collection('messages');
const userConversationsRef = firestore.collection('user_conversations');

// ==============================|| USER OPERATIONS ||============================== //

/**
 * Create or update a user in Firestore
 */
export const setUser = async (user: ChatUser): Promise<void> => {
  const userDoc = usersRef.doc(user.id);
  await userDoc.set(
    {
      ...user,
      last_active: firebase.firestore.FieldValue.serverTimestamp()
    },
    { merge: true }
  );
};

/**
 * Get a user by ID
 */
export const getUser = async (userId: string): Promise<ChatUser | null> => {
  const userDoc = usersRef.doc(userId);
  const userSnapshot = await userDoc.get();

  if (userSnapshot.exists) {
    return { id: userSnapshot.id, ...userSnapshot.data() } as ChatUser;
  }

  return null;
};

/**
 * Update user's online status
 */
export const updateUserStatus = async (userId: string, status: 'available' | 'do_not_disturb' | 'offline'): Promise<void> => {
  const userDoc = usersRef.doc(userId);
  await userDoc.update({
    online_status: status,
    last_active: firebase.firestore.FieldValue.serverTimestamp()
  });
};

/**
 * Get all users
 */
export const getAllUsers = async (): Promise<ChatUser[]> => {
  const querySnapshot = await usersRef.get();
  return querySnapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }) as ChatUser);
};

// ==============================|| CONVERSATION OPERATIONS ||============================== //

/**
 * Create a new conversation
 */
export const createConversation = async (conversation: Omit<Conversation, 'id' | 'created_at' | 'updated_at'>): Promise<string> => {
  const newConversationRef = conversationsRef.doc();
  const now = firebase.firestore.FieldValue.serverTimestamp();

  await newConversationRef.set({
    ...conversation,
    id: newConversationRef.id,
    created_at: now,
    updated_at: now
  });

  // Create user_conversation records for all participants
  for (const userId of conversation.participants) {
    const userConversationRef = userConversationsRef.doc();
    await userConversationRef.set({
      user_id: userId,
      conversation_id: newConversationRef.id,
      last_read: now,
      is_muted: false
    });
  }

  return newConversationRef.id;
};

/**
 * Get a conversation by ID
 */
export const getConversation = async (conversationId: string): Promise<Conversation | null> => {
  const conversationDoc = conversationsRef.doc(conversationId);
  const conversationSnapshot = await conversationDoc.get();

  if (conversationSnapshot.exists) {
    return { id: conversationSnapshot.id, ...conversationSnapshot.data() } as Conversation;
  }

  return null;
};

/**
 * Get all conversations for a user
 */
export const getUserConversations = async (userId: string): Promise<Conversation[]> => {
  // First get the user_conversation mappings
  const userConvQuery = userConversationsRef.where('user_id', '==', userId);
  const userConvSnapshot = await userConvQuery.get();

  const conversationIds = userConvSnapshot.docs.map((doc) => doc.data().conversation_id);

  if (conversationIds.length === 0) {
    return [];
  }

  // Then get the actual conversations
  const conversations: Conversation[] = [];

  for (const convId of conversationIds) {
    const conversation = await getConversation(convId);
    if (conversation) {
      conversations.push(conversation);
    }
  }

  return conversations;
};

/**
 * Listen to conversation updates in real-time
 */
export const subscribeToConversations = (userId: string, callback: (conversations: Conversation[]) => void) => {
  // First query user_conversations to get all conversation IDs for this user
  const userConvQuery = userConversationsRef.where('user_id', '==', userId);

  return userConvQuery.onSnapshot(async (snapshot) => {
    const conversationIds = snapshot.docs.map((doc) => doc.data().conversation_id);

    if (conversationIds.length === 0) {
      callback([]);
      return;
    }

    // Then query for all those conversations
    const conversations: Conversation[] = [];

    for (const convId of conversationIds) {
      const conversation = await getConversation(convId);
      if (conversation) {
        conversations.push(conversation);
      }
    }

    callback(conversations);
  });
};

// ==============================|| MESSAGE OPERATIONS ||============================== //

/**
 * Send a new message
 */
export const sendMessage = async (message: Omit<Message, 'id' | 'created_at'>): Promise<string> => {
  const newMessageRef = messagesRef.doc();

  await newMessageRef.set({
    ...message,
    id: newMessageRef.id,
    created_at: firebase.firestore.FieldValue.serverTimestamp(),
    is_automated: message.is_automated || false
  });

  // Update the conversation's updated_at timestamp
  const conversationRef = conversationsRef.doc(message.conversation_id);
  await conversationRef.update({
    updated_at: firebase.firestore.FieldValue.serverTimestamp()
  });

  return newMessageRef.id;
};

/**
 * Send an automated message
 */
export const sendAutomatedMessage = async (
  conversationId: string,
  text: string,
  messageType: 'text' | 'image' | 'file' = 'text',
  fileUrl?: string
): Promise<string> => {
  return sendMessage({
    conversation_id: conversationId,
    sender_id: 'system',
    text,
    is_automated: true,
    message_type: messageType,
    file_url: fileUrl
  });
};

/**
 * Get messages for a conversation
 */
export const getConversationMessages = async (conversationId: string, messageLimit = 50): Promise<Message[]> => {
  const messagesQuery = messagesRef.where('conversation_id', '==', conversationId).orderBy('created_at', 'desc').limit(messageLimit);

  const messagesSnapshot = await messagesQuery.get();

  return messagesSnapshot.docs
    .map((doc) => ({ id: doc.id, ...doc.data() }) as Message)
    .sort((a, b) => {
      // Handle timestamp comparison properly
      const aTime = a.created_at as firebase.firestore.Timestamp;
      const bTime = b.created_at as firebase.firestore.Timestamp;
      return aTime.toMillis() - bTime.toMillis();
    });
};

/**
 * Subscribe to messages in a conversation
 */
export const subscribeToMessages = (conversationId: string, callback: (messages: Message[]) => void, messageLimit = 50) => {
  const messagesQuery = messagesRef.where('conversation_id', '==', conversationId).orderBy('created_at', 'desc').limit(messageLimit);

  return messagesQuery.onSnapshot((snapshot) => {
    const messages = snapshot.docs
      .map((doc) => ({ id: doc.id, ...doc.data() }) as Message)
      .sort((a, b) => {
        // Handle timestamp comparison properly
        const aTime = a.created_at as firebase.firestore.Timestamp;
        const bTime = b.created_at as firebase.firestore.Timestamp;
        return aTime.toMillis() - bTime.toMillis();
      });

    callback(messages);
  });
};

/**
 * Mark conversation as read
 */
export const markConversationAsRead = async (userId: string, conversationId: string): Promise<void> => {
  // Find the user_conversation document
  const userConvQuery = userConversationsRef.where('user_id', '==', userId).where('conversation_id', '==', conversationId);

  const userConvSnapshot = await userConvQuery.get();

  if (!userConvSnapshot.empty) {
    const userConvDoc = userConvSnapshot.docs[0];
    await userConversationsRef.doc(userConvDoc.id).update({
      last_read: firebase.firestore.FieldValue.serverTimestamp()
    });
  }
};

// ==============================|| DIRECT CHAT HELPERS ||============================== //

/**
 * Find or create a direct chat between two users
 */
export const findOrCreateDirectChat = async (currentUserId: string, otherUserId: string): Promise<string> => {
  // Check if a direct conversation already exists between these users
  const participants1 = [currentUserId, otherUserId];
  const participants2 = [otherUserId, currentUserId];

  const conversationsQuery1 = conversationsRef.where('type', '==', 'direct').where('participants', '==', participants1);

  const conversationsQuery2 = conversationsRef.where('type', '==', 'direct').where('participants', '==', participants2);

  const [snapshot1, snapshot2] = await Promise.all([conversationsQuery1.get(), conversationsQuery2.get()]);

  // If conversation exists, return its id
  if (!snapshot1.empty) {
    return snapshot1.docs[0].id;
  }

  if (!snapshot2.empty) {
    return snapshot2.docs[0].id;
  }

  // Otherwise create a new conversation
  return createConversation({
    participants: [currentUserId, otherUserId],
    type: 'direct'
  });
};
