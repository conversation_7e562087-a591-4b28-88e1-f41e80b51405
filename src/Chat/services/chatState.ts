import { useState, useCallback, useEffect } from 'react';
import chatService from './chatService';
import { Conversation, Message, ChatUser } from '../types/models';

// State management hook using React hooks instead of Redux
export const useChatState = () => {
  // State
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [currentConversationId, setCurrentConversationId] = useState<string | null>(null);
  const [messagesMap, setMessagesMap] = useState<Record<string, Message[]>>({});
  const [currentUser, setCurrentUser] = useState<ChatUser | null>(null);
  const [allUsers, setAllUsers] = useState<ChatUser[]>([]);

  // Cleanup subscriptions on unmount
  useEffect(() => {
    return () => {
      chatService.clearCurrentUser();
    };
  }, []);

  // Initialize chat service with current user
  const initChat = useCallback(async (userId: string) => {
    try {
      setLoading(true);
      setError(null);
      chatService.setCurrentUser(userId);

      // Get current user data
      const user = await chatService.getCurrentUser();
      if (user) {
        setCurrentUser(user);
      }

      // Get all conversations for the user
      const userConversations = await chatService.getConversations();
      setConversations(userConversations);

      // Get all users
      const users = await chatService.getUsers();
      setAllUsers(users);

      // Subscribe to conversation updates
      chatService.subscribeToConversations((updatedConversations) => {
        setConversations(updatedConversations);
      });
    } catch (err) {
      console.error('Error initializing chat:', err);
      setError(err instanceof Error ? err.message : 'Error initializing chat');
    } finally {
      setLoading(false);
    }
  }, []);

  // Load messages for a conversation
  const loadMessages = useCallback(async (conversationId: string) => {
    try {
      setLoading(true);
      setError(null);
      setCurrentConversationId(conversationId);

      // Get messages
      const messages = await chatService.getMessages(conversationId);
      setMessagesMap((prev) => ({
        ...prev,
        [conversationId]: messages
      }));

      // Subscribe to message updates
      chatService.subscribeToMessages(conversationId, (updatedMessages) => {
        setMessagesMap((prev) => ({
          ...prev,
          [conversationId]: updatedMessages
        }));
      });

      // Mark conversation as read
      await chatService.markConversationAsRead(conversationId);
    } catch (err) {
      console.error('Error loading messages:', err);
      setError(err instanceof Error ? err.message : 'Error loading messages');
    } finally {
      setLoading(false);
    }
  }, []);

  // Send a message
  const sendMessage = useCallback(
    async (conversationId: string, text: string, messageType: 'text' | 'image' | 'file' = 'text', fileUrl?: string) => {
      try {
        setError(null);
        await chatService.sendMessage(conversationId, text, messageType, fileUrl);
      } catch (err) {
        console.error('Error sending message:', err);
        setError(err instanceof Error ? err.message : 'Error sending message');
      }
    },
    []
  );

  // Start a new direct chat
  const startDirectChat = useCallback(
    async (otherUserId: string) => {
      try {
        setLoading(true);
        setError(null);
        const conversationId = await chatService.findOrCreateDirectChat(otherUserId);
        setCurrentConversationId(conversationId);
        await loadMessages(conversationId);
      } catch (err) {
        console.error('Error starting direct chat:', err);
        setError(err instanceof Error ? err.message : 'Error starting direct chat');
      } finally {
        setLoading(false);
      }
    },
    [loadMessages]
  );

  // Create a group conversation
  const createGroupChat = useCallback(
    async (participants: string[], title: string) => {
      try {
        setLoading(true);
        setError(null);
        const conversationId = await chatService.createGroupConversation(participants, title);
        setCurrentConversationId(conversationId);
        await loadMessages(conversationId);
      } catch (err) {
        console.error('Error creating group chat:', err);
        setError(err instanceof Error ? err.message : 'Error creating group chat');
      } finally {
        setLoading(false);
      }
    },
    [loadMessages]
  );

  return {
    // State
    loading,
    error,
    conversations,
    currentConversationId,
    messages: messagesMap,
    currentUser,
    allUsers,

    // Actions
    initChat,
    loadMessages,
    sendMessage,
    startDirectChat,
    createGroupChat
  };
};
