import React from 'react';
import { Card, CardContent, Typography, Avatar, Box } from '@mui/material';
import CountUp from 'react-countup';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import TrendingDownIcon from '@mui/icons-material/TrendingDown';

interface AdminStatCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  color: string;
  gradient?: string;
  trend?: 'up' | 'down' | null;
  trendValue?: number;
  ribbonText?: string;
}

const AdminStatCard: React.FC<AdminStatCardProps> = ({
  title,
  value,
  icon,
  color,
  gradient = 'linear-gradient(135deg, #1976d2 0%, #42a5f5 100%)',
  trend = null,
  trendValue,
  ribbonText
}) => (
  <Card
    elevation={5}
    sx={{
      borderRadius: 3,
      overflow: 'visible',
      position: 'relative',
      background: gradient,
      color: '#fff',
      boxShadow: '0 8px 32px 0 rgba(31, 38, 135, 0.15)',
      minHeight: 120,
      mb: 1
    }}
  >
    {ribbonText && (
      <Box
        sx={{
          position: 'absolute',
          top: 14,
          right: -32,
          bgcolor: color,
          color: '#fff',
          px: 2,
          py: 0.5,
          borderRadius: 2,
          fontWeight: 700,
          fontSize: 13,
          transform: 'rotate(20deg)',
          zIndex: 2
        }}
      >
        {ribbonText}
      </Box>
    )}
    <CardContent sx={{ display: 'flex', alignItems: 'center', position: 'relative' }}>
      <Avatar
        sx={{
          bgcolor: 'rgba(255,255,255,0.13)',
          color: color,
          width: 64,
          height: 64,
          mr: 3,
          fontSize: 36,
          boxShadow: '0 2px 12px 0 rgba(31, 38, 135, 0.12)',
          position: 'relative',
          zIndex: 1
        }}
      >
        {icon}
      </Avatar>
      <Box sx={{ flex: 1, minWidth: 0 }}>
        <Typography variant="subtitle2" sx={{ opacity: 0.85, mb: 0.5 }}>
          {title}
        </Typography>
        <Typography variant="h3" sx={{ fontWeight: 800, letterSpacing: -1 }}>
          <CountUp end={typeof value === 'number' ? value : parseFloat(value)} duration={1.2} separator="," />
        </Typography>
        {trend && (
          <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
            {trend === 'up' ? (
              <TrendingUpIcon sx={{ color: '#00e676', mr: 0.5, fontSize: 18 }} />
            ) : (
              <TrendingDownIcon sx={{ color: '#ff1744', mr: 0.5, fontSize: 18 }} />
            )}
            <Typography variant="caption" sx={{ color: trend === 'up' ? '#00e676' : '#ff1744', fontWeight: 700 }}>
              {trendValue ? `${trendValue > 0 ? '+' : ''}${trendValue}%` : ''}
            </Typography>
          </Box>
        )}
      </Box>
      {/* Large faded icon in background */}
      <Box
        sx={{
          position: 'absolute',
          right: 12,
          bottom: 8,
          opacity: 0.18,
          fontSize: 84,
          zIndex: 0,
          pointerEvents: 'none'
        }}
      >
        {icon}
      </Box>
    </CardContent>
  </Card>
);

export default AdminStatCard;
