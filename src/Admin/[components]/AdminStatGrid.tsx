import React from 'react';
import { Grid } from '@mui/material';
import AdminStatCard from './AdminStatCard';
import PersonIcon from '@mui/icons-material/Person';
import LocalHospitalIcon from '@mui/icons-material/LocalHospital';
import PeopleIcon from '@mui/icons-material/People';
import BusinessIcon from '@mui/icons-material/Business';
import DescriptionIcon from '@mui/icons-material/Description';
import AssessmentIcon from '@mui/icons-material/Assessment';
import MonetizationOnIcon from '@mui/icons-material/MonetizationOn';
import StarIcon from '@mui/icons-material/Star';
import EmojiEventsIcon from '@mui/icons-material/EmojiEvents';

interface AdminStatGridProps {
  stats: {
    users: number;
    doctors: number;
    patients: number;
    clinics: number;
    questionnaires: number;
    reports: number;
    activeUsers?: number;
    [key: string]: any; // Allow additional dynamic keys for statConfigs
  };
}

// Refined, less flashy, more variety in tile style
const statConfigs = [
  {
    key: 'users',
    title: 'Users',
    icon: <PersonIcon fontSize="large" />,
    color: '#1976d2',
    gradient: undefined, // Use default card
    ribbonText: undefined,
    trend: undefined,
    trendValue: undefined
  },
  {
    key: 'doctors',
    title: 'Doctors',
    icon: <LocalHospitalIcon fontSize="large" />,
    color: '#388e3c',
    gradient: 'linear-gradient(90deg, #f8fafc 0%, #e3f2fd 100%)',
    ribbonText: 'Medical',
    trend: 'up',
    trendValue: 1.2
  },
  {
    key: 'patients',
    title: 'Patients',
    icon: <PeopleIcon fontSize="large" />,
    color: '#0288d1',
    gradient: undefined,
    ribbonText: undefined,
    trend: 'up',
    trendValue: 2.4
  },
  {
    key: 'clinics',
    title: 'Clinics',
    icon: <BusinessIcon fontSize="large" />,
    color: '#fbc02d',
    gradient: 'linear-gradient(90deg, #fffde7 0%, #fff9c4 100%)',
    ribbonText: undefined,
    trend: undefined,
    trendValue: undefined
  },
  {
    key: 'questionnaires',
    title: 'Questionnaires',
    icon: <DescriptionIcon fontSize="large" />,
    color: '#7b1fa2',
    gradient: undefined,
    ribbonText: 'Forms',
    trend: undefined,
    trendValue: undefined
  },
  {
    key: 'reports',
    title: 'Reports',
    icon: <AssessmentIcon fontSize="large" />,
    color: '#d32f2f',
    gradient: 'linear-gradient(90deg, #fff3e0 0%, #ffebee 100%)',
    ribbonText: undefined,
    trend: 'down',
    trendValue: -0.3
  },
  {
    key: 'revenue',
    title: 'Revenue',
    icon: <MonetizationOnIcon fontSize="large" />,
    color: '#00bfae',
    gradient: undefined,
    ribbonText: undefined,
    trend: undefined,
    trendValue: undefined,
    value: '$12,400'
  },
  {
    key: 'starred',
    title: 'Featured',
    icon: <StarIcon fontSize="large" />,
    color: '#ffd600',
    gradient: undefined,
    ribbonText: 'Top',
    trend: undefined,
    trendValue: undefined,
    value: 7
  },
  {
    key: 'awards',
    title: 'Awards',
    icon: <EmojiEventsIcon fontSize="large" />,
    color: '#ff9800',
    gradient: 'linear-gradient(90deg, #fbe9e7 0%, #fffde7 100%)',
    ribbonText: undefined,
    trend: undefined,
    trendValue: undefined,
    value: 3
  }
];

const AdminStatGrid: React.FC<AdminStatGridProps> = ({ stats }) => (
  <Grid container spacing={3}>
    {statConfigs.map((cfg) => {
      const value = typeof stats[cfg.key] !== 'undefined' ? stats[cfg.key] : cfg.value || 0;
      return (
        <Grid item xs={12} sm={6} md={3} key={cfg.key}>
          <AdminStatCard
            title={cfg.title}
            value={value}
            icon={cfg.icon}
            color={cfg.color}
            gradient={cfg.gradient}
            ribbonText={cfg.ribbonText}
            trend={cfg.trend === 'up' || cfg.trend === 'down' ? cfg.trend : undefined}
            trendValue={cfg.trendValue}
          />
        </Grid>
      );
    })}
  </Grid>
);

export default AdminStatGrid;
