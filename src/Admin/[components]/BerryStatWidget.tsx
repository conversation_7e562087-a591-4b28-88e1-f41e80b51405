import React from 'react';
import { Card, Box, Typography } from '@mui/material';
import Chart from 'react-apexcharts';

interface BerryStatWidgetProps {
  value: number | string;
  label: string;
  percentage: number;
  color: string; // main background color
  chartData: number[];
  chartColor: string;
}

const BerryStatWidget: React.FC<BerryStatWidgetProps> = ({ value, label, percentage, color, chartData, chartColor }) => {
  const chartOptions = {
    chart: {
      id: 'sparkline',
      sparkline: { enabled: true },
      animations: { enabled: false }
    },
    stroke: { curve: 'smooth' as const, width: 3 },
    fill: { type: 'solid', opacity: 0.25 },
    colors: [chartColor],
    tooltip: { enabled: false },
    grid: { show: false },
    xaxis: { labels: { show: false }, axisBorder: { show: false }, axisTicks: { show: false } },
    yaxis: { show: false }
  };

  return (
    <Card
      sx={{
        background: color,
        borderRadius: 3,
        p: 2.5,
        minHeight: 150,
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between',
        boxShadow: '0 4px 24px 0 rgba(31, 38, 135, 0.12)',
        color: '#fff',
        position: 'relative',
        overflow: 'hidden'
      }}
    >
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
        <Box>
          <Typography variant="h4" sx={{ fontWeight: 800, lineHeight: 1 }}>
            {value}
          </Typography>
          <Typography variant="subtitle1" sx={{ opacity: 0.9 }}>
            {label}
          </Typography>
        </Box>
        <Typography variant="h6" sx={{ opacity: 0.8 }}>
          {percentage}%
        </Typography>
      </Box>
      <Box sx={{ position: 'absolute', left: 0, bottom: 0, width: '100%' }}>
        <Chart options={chartOptions} series={[{ data: chartData }]} type="area" height={48} />
      </Box>
    </Card>
  );
};

export default BerryStatWidget;
