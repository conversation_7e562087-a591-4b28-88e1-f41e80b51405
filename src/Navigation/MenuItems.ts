import { NavItemType } from '[types]/menu';
import ROUTES from 'Routing/appRoutes';

// --- ATOMIC NAV ITEMS (no groups, only items or collapses) ---

export const AI_CHAT_MENU_ITEM: NavItemType = {
  id: 'ai-chat',
  title: 'AI Chat Assistant',
  type: 'item',
  url: ROUTES.AI_CHAT.BASE,
  icon: { type: 'tabler', value: 'IconRobot' },
  breadcrumbs: false
};

export const CLINIC_PROFILE_MENU_ITEM: NavItemType = {
  id: 'clinic-profile',
  title: 'Clinic Profile',
  type: 'item',
  url: ROUTES.CLINICS.PROFILE,
  icon: { type: 'tabler', value: 'IconBuildingHospital' },
  breadcrumbs: false
};

export const ALL_PRODUCTS_MENU_ITEM: NavItemType = {
  id: 'all-products',
  title: 'All Products',
  type: 'item',
  url: ROUTES.PRODUCTS.LIST,
  icon: { type: 'tabler', value: 'IconShoppingBag' },
  breadcrumbs: false
};

export const ADD_PRODUCT_MENU_ITEM: NavItemType = {
  id: 'add-product',
  title: 'Add Product',
  type: 'item',
  url: ROUTES.PRODUCTS.ADD,
  icon: { type: 'tabler', value: 'IconPlus' },
  breadcrumbs: false
};

export const EDIT_PRODUCT_MENU_ITEM: NavItemType = {
  id: 'edit-product',
  title: 'Edit Product',
  type: 'item',
  url: ROUTES.PRODUCTS.EDIT(':id'),
  icon: { type: 'tabler', value: 'IconEdit' },
  breadcrumbs: false
};

export const DETAILS_PRODUCT_MENU_ITEM: NavItemType = {
  id: 'details-product',
  title: 'Details Product',
  type: 'item',
  url: ROUTES.PRODUCTS.DETAILS(':id'),
  icon: { type: 'tabler', value: 'IconInfo' },
  breadcrumbs: false
};

export const MY_PURCHASES_MENU_ITEM: NavItemType = {
  id: 'my-purchases',
  title: 'My Purchases',
  type: 'item',
  url: '/trq/purchases/my-purchases',
  icon: { type: 'tabler', value: 'IconShoppingCart' },
  breadcrumbs: false
};

export const PURCHASES_LIST_MENU_ITEM: NavItemType = {
  id: 'purchases-list',
  title: 'Purchases',
  type: 'item',
  url: ROUTES.PURCHASES.LIST,
  icon: { type: 'tabler', value: 'IconShoppingCart' },
  breadcrumbs: false
};

export const TEMPLATES_MENU_ITEM: NavItemType = {
  id: 'templates',
  title: 'Templates',
  type: 'item',
  url: ROUTES.TEMPLATES.LIST,
  icon: { type: 'tabler', value: 'IconTemplate' },
  breadcrumbs: false
};

export const ALL_PATIENTS_MENU_ITEM: NavItemType = {
  id: 'all-patients',
  title: 'Patients',
  type: 'item',
  url: ROUTES.PATIENTS.LIST,
  icon: { type: 'tabler', value: 'IconUsers' },
  breadcrumbs: false
};

export const PATIENT_HOME_MENU_ITEM: NavItemType = {
  id: 'trq-patient-home',
  title: 'Patient Home',
  type: 'item',
  url: ROUTES.HOMEPAGES.PATIENT,
  icon: { type: 'tabler', value: 'IconHome' },
  breadcrumbs: false
};

export const CLIENT_DASHBOARD_MENU_ITEM: NavItemType = {
  id: 'client-dashboard',
  title: 'Dashboard',
  type: 'item',
  url: ROUTES.HOMEPAGES.CLIENT,
  icon: { type: 'tabler', value: 'IconDashboard' },
  breadcrumbs: false
};

export const DOCTOR_DASHBOARD_MENU_ITEM: NavItemType = {
  id: 'trq-dashboard',
  title: 'Dashboard',
  type: 'item',
  url: ROUTES.HOMEPAGES.DOCTOR,
  icon: { type: 'tabler', value: 'IconDashboard' },
  breadcrumbs: false
};

export const CLINIC_ADMIN_DASHBOARD_MENU_ITEM: NavItemType = {
  id: 'trq-dashboard',
  title: 'Dashboard',
  type: 'item',
  url: ROUTES.HOMEPAGES.CLINIC_ADMIN,
  icon: { type: 'tabler', value: 'IconDashboard' },
  breadcrumbs: false
};

export const ADMIN_HOME_MENU_ITEM: NavItemType = {
  id: 'admin-home',
  title: 'Home',
  type: 'item',
  url: ROUTES.HOMEPAGES.ADMIN,
  icon: { type: 'tabler', value: 'IconHome' },
  breadcrumbs: false
};

export const MY_QUESTIONNAIRES_MENU_ITEM: NavItemType = {
  id: 'my-questionnaires',
  title: 'My Questionnaires',
  type: 'item',
  url: ROUTES.QUESTIONNAIRES.MY_QUESTIONNAIRES,
  icon: { type: 'tabler', value: 'IconClipboardList' },
  breadcrumbs: false
};

export const ALL_QUESTIONNAIRES_MENU_ITEM: NavItemType = {
  id: 'questionnaires',
  title: 'Questionnaires',
  type: 'item',
  url: ROUTES.QUESTIONNAIRES.LIST,
  icon: { type: 'tabler', value: 'IconClipboardList' },
  breadcrumbs: false
};

export const CLINIC_ADMIN_QUESTIONNAIRES_MENU_ITEM: NavItemType = {
  id: 'questionnaires',
  title: 'Questionnaires',
  type: 'item',
  url: ROUTES.QUESTIONNAIRES.LIST,
  icon: { type: 'tabler', value: 'IconClipboardList' },
  breadcrumbs: false
};

export const DOCTOR_COMPLIANCE_REPORTS_MENU_ITEM: NavItemType = {
  id: 'compliance-reports',
  title: 'Compliance Reports',
  icon: { type: 'tabler', value: 'IconReportMedical' },
  type: 'item',
  url: ROUTES.COMPLIANCE_REPORTS.LIST,
  breadcrumbs: false
};

export const CLINIC_ADMIN_COMPLIANCE_REPORTS_MENU_ITEM: NavItemType = {
  id: 'compliance-reports',
  title: 'Compliance Reports',
  icon: { type: 'tabler', value: 'IconReportMedical' },
  type: 'item',
  url: ROUTES.COMPLIANCE_REPORTS.LIST,
  breadcrumbs: false
};

export const ADMIN_COMPLIANCE_REPORTS_MENU_ITEM: NavItemType = {
  id: 'compliance-reports',
  title: 'Reports',
  icon: { type: 'tabler', value: 'IconReportMedical' },
  type: 'item',
  url: ROUTES.COMPLIANCE_REPORTS.LIST,
  breadcrumbs: false
};

export const CLIENTS_MENU_ITEM: NavItemType = {
  id: 'clients',
  title: 'Clients',
  type: 'item',
  url: ROUTES.CLIENTS.LIST,
  icon: { type: 'tabler', value: 'IconUserCircle' },
  breadcrumbs: false
};

export const DOCTORS_MENU_ITEM: NavItemType = {
  id: 'doctors',
  title: 'Doctors',
  type: 'item',
  url: ROUTES.DOCTORS.LIST,
  icon: { type: 'tabler', value: 'IconStethoscope' },
  breadcrumbs: false
};

export const CLINIC_ADMINS_MENU_ITEM: NavItemType = {
  id: 'clinic-admins',
  title: 'Clinic Admins',
  type: 'item',
  url: ROUTES.CLINIC_ADMINS.LIST,
  icon: { type: 'tabler', value: 'IconUserCog' },
  breadcrumbs: false
};

export const ALL_USERS_MENU_ITEM: NavItemType = {
  id: 'all-users',
  title: 'All Users',
  type: 'item',
  url: ROUTES.USERS.LIST,
  icon: { type: 'tabler', value: 'IconUsers' },
  breadcrumbs: false
};

export const ADMIN_CLINICS_MENU_ITEM: NavItemType = {
  id: 'clinics',
  title: 'Clinics',
  type: 'item',
  url: ROUTES.CLINICS.LIST,
  icon: { type: 'tabler', value: 'IconHospital' },
  breadcrumbs: false
};

// --- User Migration Menu Item ---
export const USER_MIGRATION_MENU_ITEM: NavItemType = {
  id: 'user-migration',
  title: 'User Migration',
  type: 'item',
  url: ROUTES.USERS.MIGRATION,
  icon: { type: 'tabler', value: 'IconTransfer' },
  breadcrumbs: false
};

export const CHAT_MENU_ITEM: NavItemType = {
  id: 'real-time-chat',
  title: 'Chat & Messaging',
  type: 'item',
  url: '/trq/chat',
  icon: { type: 'tabler', value: 'IconMessageCircle' },
  breadcrumbs: false
};

// --- Dynamic Menu Item Creators ---
export const PATIENT_PROFILE_MENU_ITEM = (userId: string): NavItemType => ({
  id: 'patient-profile',
  title: 'My Profile',
  type: 'item',
  url: ROUTES.PATIENTS.PROFILE(userId),
  icon: { type: 'tabler', value: 'IconUser' },
  breadcrumbs: false
});

export const MY_DOCTOR_MENU_ITEM: NavItemType = {
  id: 'my-doctor',
  title: 'My Doctor',
  type: 'item',
  url: ROUTES.DOCTORS.MY_DOCTOR,
  icon: { type: 'tabler', value: 'IconStethoscope' },
  breadcrumbs: false
};

export const MY_CLINIC_MENU_ITEM: NavItemType = {
  id: 'my-clinic',
  title: 'My Clinic',
  type: 'item',
  url: ROUTES.CLINICS.MY_CLINIC,
  icon: { type: 'tabler', value: 'IconHospital' },
  breadcrumbs: false
};

export const CLIENT_DETAILS_MENU_ITEM = (userId: string): NavItemType => ({
  id: 'clients',
  title: 'Details',
  type: 'item',
  url: ROUTES.CLIENTS.DETAILS(userId),
  icon: { type: 'tabler', value: 'IconUserCircle' },
  breadcrumbs: false
});

export const CLIENT_PROFILE_MENU_ITEM = (userId: string): NavItemType => ({
  id: 'client-profile',
  title: 'Profile',
  type: 'item',
  url: ROUTES.CLIENTS.PROFILE(userId),
  icon: { type: 'tabler', value: 'IconUser' },
  breadcrumbs: false
});

// --- Linear Integration Menu Items ---
export const LINEAR_DASHBOARD_MENU_ITEM: NavItemType = {
  id: 'linear-dashboard',
  title: 'Linear Dashboard',
  type: 'item',
  url: ROUTES.LINEAR.DASHBOARD,
  icon: { type: 'tabler', value: 'IconDashboard' },
  breadcrumbs: false
};

export const LINEAR_PROJECTS_MENU_ITEM: NavItemType = {
  id: 'linear-projects',
  title: 'Projects',
  type: 'item',
  url: ROUTES.LINEAR.PROJECTS,
  icon: { type: 'tabler', value: 'IconFolder' },
  breadcrumbs: false
};

export const LINEAR_ISSUES_MENU_ITEM: NavItemType = {
  id: 'linear-issues',
  title: 'Issues',
  type: 'item',
  url: ROUTES.LINEAR.ISSUES,
  icon: { type: 'tabler', value: 'IconBug' },
  breadcrumbs: false
};

export const LINEAR_SETTINGS_MENU_ITEM: NavItemType = {
  id: 'linear-settings',
  title: 'Linear Settings',
  type: 'item',
  url: ROUTES.LINEAR.SETTINGS,
  icon: { type: 'tabler', value: 'IconSettings' },
  breadcrumbs: false
};

export const LINEAR_MENU_GROUP: NavItemType = {
  id: 'linear-group',
  title: 'Linear Integration',
  type: 'group',
  children: [LINEAR_DASHBOARD_MENU_ITEM, LINEAR_PROJECTS_MENU_ITEM, LINEAR_ISSUES_MENU_ITEM, LINEAR_SETTINGS_MENU_ITEM]
};
