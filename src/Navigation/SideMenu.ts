import { useMemo } from 'react';
import { NavItemType } from '[types]/menu';
import { Role } from 'RBAC/[types]/Role';
import {
  AI_CHAT_MENU_ITEM,
  CLINIC_PROFILE_MENU_ITEM,
  MY_PURCHASES_MENU_ITEM,
  TEMPLATES_MENU_ITEM,
  ALL_PATIENTS_MENU_ITEM,
  PATIENT_HOME_MENU_ITEM,
  CLIENT_DASHBOARD_MENU_ITEM,
  DOCTOR_DASHBOARD_MENU_ITEM,
  ADMIN_HOME_MENU_ITEM,
  MY_QUESTIONNAIRES_MENU_ITEM,
  ADMIN_CLINICS_MENU_ITEM,
  PATIENT_PROFILE_MENU_ITEM,
  MY_DOCTOR_MENU_ITEM,
  MY_CLINIC_MENU_ITEM,
  CLIENT_PROFILE_MENU_ITEM,
  DOCTORS_MENU_ITEM,
  CLIENTS_MENU_ITEM,
  CLINIC_ADMINS_MENU_ITEM,
  ALL_USERS_MENU_ITEM,
  ALL_PRODUCTS_MENU_ITEM,
  ALL_QUESTIONNAIRES_MENU_ITEM,
  USER_MIGRATION_MENU_ITEM,
  CHAT_MENU_ITEM,
  PURCHASES_LIST_MENU_ITEM
} from './MenuItems';
import ROUTES from 'Routing/appRoutes';

// --- Role-based menu groups with inline collapses/groups ---

export const PATIENT_MENU = (userId: string): NavItemType => ({
  id: 'patient-menu',
  title: 'Patient Menu',
  type: 'group',
  children: [
    { ...PATIENT_HOME_MENU_ITEM, icon: { type: 'tabler', value: 'IconHome' } },
    { ...MY_QUESTIONNAIRES_MENU_ITEM, icon: { type: 'tabler', value: 'IconClipboardList' } },
    { ...PATIENT_PROFILE_MENU_ITEM(userId), icon: { type: 'tabler', value: 'IconUser' } },
    { ...MY_DOCTOR_MENU_ITEM, icon: { type: 'tabler', value: 'IconStethoscope' } },
    { ...MY_CLINIC_MENU_ITEM, icon: { type: 'tabler', value: 'IconBuildingHospital' } },
    { ...AI_CHAT_MENU_ITEM, icon: { type: 'tabler', value: 'IconRobot' } },
    { ...CHAT_MENU_ITEM, icon: { type: 'tabler', value: 'IconMessage' } }
  ]
});

export const CLIENT_MENU = (userId: string): NavItemType => ({
  id: 'client-menu',
  title: 'Client Menu',
  type: 'group',
  children: [
    { ...CLIENT_DASHBOARD_MENU_ITEM, icon: { type: 'tabler', value: 'IconHome' } },
    {
      id: 'products',
      title: 'Products',
      type: 'collapse',
      icon: { type: 'tabler', value: 'IconShoppingBag' },
      children: [ALL_PRODUCTS_MENU_ITEM]
    },
    { ...MY_PURCHASES_MENU_ITEM, icon: { type: 'tabler', value: 'IconShoppingCart' } },
    { ...ALL_PATIENTS_MENU_ITEM, icon: { type: 'tabler', value: 'IconUsers' } },
    { ...CLIENT_PROFILE_MENU_ITEM(userId), icon: { type: 'tabler', value: 'IconUser' } },
    { ...MY_CLINIC_MENU_ITEM, icon: { type: 'tabler', value: 'IconBuildingHospital' } },
    { ...AI_CHAT_MENU_ITEM, icon: { type: 'tabler', value: 'IconRobot' } },
    { ...CHAT_MENU_ITEM, icon: { type: 'tabler', value: 'IconMessage' } }
  ]
});

export const DOCTOR_MENU = (userId: string, clinicId?: string): NavItemType => ({
  id: 'doctor-menu',
  title: 'Doctor Menu',
  type: 'group',
  children: [
    { ...DOCTOR_DASHBOARD_MENU_ITEM, icon: { type: 'tabler', value: 'IconHome' } },
    {
      id: 'questionnaires',
      title: 'Questionnaires',
      type: 'item',
      url: ROUTES.QUESTIONNAIRES.LIST,
      icon: { type: 'tabler', value: 'IconClipboardList' },
      breadcrumbs: false,
      children: [ALL_QUESTIONNAIRES_MENU_ITEM, TEMPLATES_MENU_ITEM]
    },
    {
      id: 'user-management',
      title: 'User Management',
      type: 'collapse',
      icon: { type: 'tabler', value: 'IconUsers' },
      children: [ALL_PATIENTS_MENU_ITEM, CLINIC_ADMINS_MENU_ITEM, CLIENTS_MENU_ITEM]
    },
    { ...AI_CHAT_MENU_ITEM, icon: { type: 'tabler', value: 'IconRobot' } },
    { ...CHAT_MENU_ITEM, icon: { type: 'tabler', value: 'IconMessage' } },
    { ...MY_CLINIC_MENU_ITEM, icon: { type: 'tabler', value: 'IconBuildingHospital' } },
    { ...CLINIC_PROFILE_MENU_ITEM, icon: { type: 'tabler', value: 'IconBuildingHospital' } }
  ]
});

export const CLINIC_ADMIN_MENU = (userId: string): NavItemType => ({
  id: 'clinic-admin-menu',
  title: 'Clinic Admin Menu',
  type: 'group',
  children: [
    { ...CLIENT_DASHBOARD_MENU_ITEM, icon: { type: 'tabler', value: 'IconHome' } },
    {
      id: 'user-management',
      title: 'User Management',
      type: 'collapse',
      icon: { type: 'tabler', value: 'IconUsers' },
      children: [ALL_PATIENTS_MENU_ITEM, CLIENTS_MENU_ITEM]
    },
    {
      id: 'trq-settings',
      title: 'Clinic Settings',
      type: 'item',
      url: ROUTES.OTHERS.SETTINGS,
      icon: { type: 'tabler', value: 'IconSettings' },
      breadcrumbs: false
    },
    { ...AI_CHAT_MENU_ITEM, icon: { type: 'tabler', value: 'IconRobot' } },
    { ...CHAT_MENU_ITEM, icon: { type: 'tabler', value: 'IconMessage' } },
    {
      id: 'products',
      title: 'Products',
      type: 'collapse',
      icon: { type: 'tabler', value: 'IconShoppingBag' },
      children: [ALL_PRODUCTS_MENU_ITEM]
    },
    { ...MY_CLINIC_MENU_ITEM, icon: { type: 'tabler', value: 'IconBuildingHospital' } },
    { ...CLINIC_PROFILE_MENU_ITEM, icon: { type: 'tabler', value: 'IconBuildingHospital' } },
    {
      id: 'purchases',
      title: 'Purchases',
      type: 'collapse',
      icon: { type: 'tabler', value: 'IconShoppingCart' },
      children: [MY_PURCHASES_MENU_ITEM, PURCHASES_LIST_MENU_ITEM]
    }
  ]
});

export const ADMIN_MENU = (userId?: string): NavItemType => ({
  id: 'admin-menu',
  title: 'Admin Menu',
  type: 'group',
  children: [
    { ...ADMIN_HOME_MENU_ITEM, icon: { type: 'tabler', value: 'IconHome' } },
    // MY_DOCTOR_MENU_ITEM(userId),
    // MY_CLINIC_MENU_ITEM(userId),
    {
      id: 'questionnaires',
      title: 'Questionnaires',
      type: 'collapse',
      icon: { type: 'tabler', value: 'IconFileText' },
      breadcrumbs: false,
      children: [ALL_QUESTIONNAIRES_MENU_ITEM, TEMPLATES_MENU_ITEM]
    },
    {
      id: 'products',
      title: 'Products',
      type: 'collapse',
      icon: { type: 'tabler', value: 'IconShoppingBag' },
      children: [ALL_PRODUCTS_MENU_ITEM]
    },
    { ...PURCHASES_LIST_MENU_ITEM, title: 'Purchases', type: 'item', icon: { type: 'tabler', value: 'IconShoppingCart' } },
    {
      id: 'user-management',
      title: 'User Management',
      type: 'collapse',
      icon: { type: 'tabler', value: 'IconUsers' },
      children: [
        ALL_PATIENTS_MENU_ITEM,
        CLIENTS_MENU_ITEM,
        DOCTORS_MENU_ITEM,
        CLINIC_ADMINS_MENU_ITEM,
        ALL_USERS_MENU_ITEM,
        USER_MIGRATION_MENU_ITEM
      ]
    },
    {
      id: 'admin',
      title: 'Admin',
      type: 'collapse',
      icon: { type: 'tabler', value: 'IconSettings' },
      children: [
        {
          id: 'roles',
          title: 'Role Management',
          type: 'item',
          url: ROUTES.ADMIN.ROLES,
          icon: { type: 'tabler', value: 'IconUserCircle' },
          breadcrumbs: false
        },
        {
          id: 'permissions',
          title: 'Permissions',
          type: 'item',
          url: ROUTES.ADMIN.PERMISSIONS,
          icon: { type: 'tabler', value: 'IconSettings' },
          breadcrumbs: false
        },
        {
          id: 'site-analytics',
          title: 'Analytics',
          type: 'item',
          url: ROUTES.OTHERS.ANALYTICS,
          icon: { type: 'tabler', value: 'IconChartInfographic' },
          breadcrumbs: false
        }
      ]
    },
    { ...ADMIN_CLINICS_MENU_ITEM, icon: { type: 'tabler', value: 'IconBuildingHospital' } },
    { ...AI_CHAT_MENU_ITEM, icon: { type: 'tabler', value: 'IconRobot' } },
    { ...CHAT_MENU_ITEM, icon: { type: 'tabler', value: 'IconMessage' } },
    {
      id: 'trq-settings',
      title: 'Settings',
      type: 'item',
      url: ROUTES.OTHERS.SETTINGS,
      icon: { type: 'tabler', value: 'IconSettings' },
      breadcrumbs: false
    }
  ]
});

export function getMenuItems(role: Role | null, userId: string): { items: NavItemType[] } {
  switch (role) {
    case Role.Patient:
      return { items: [PATIENT_MENU(userId)] };
    case Role.Client:
      return { items: [CLIENT_MENU(userId)] };
    case Role.Doctor:
      return { items: [DOCTOR_MENU(userId)] };
    case Role.ClinicAdmin:
      return { items: [CLINIC_ADMIN_MENU(userId)] };
    case Role.Admin:
      return { items: [ADMIN_MENU(userId)] };
    default:
      return { items: [] };
  }
}

export function useMenuItems(role: Role | null, userId: string) {
  return useMemo(() => getMenuItems(role, userId), [role, userId]);
}

export default useMenuItems;
