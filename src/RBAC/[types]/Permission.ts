import { useIntl } from 'react-intl';
import { RESOURCES, ACTIONS } from 'RBAC/permissions';
import { Role } from 'RBAC/[types]/Role';
// Define the valid action types
type ActionType = 'create' | 'read' | 'update' | 'delete' | 'list' | 'manage';

/**
 * Defines the structure for a permission
 */
export type Permission = {
  resource: string;
  actions: ActionType[];
};

/**
 * Defines the structure for a role with its permissions
 */
export type RolePermission = {
  id: string;
  name: string;
  description: string;
  permissions: Permission[];
};

/**
 * Helper function to get translated role permissions
 * Must be used within a component with React hooks
 */
export const useRolePermissions = (): Record<Role, RolePermission> => {
  const intl = useIntl();

  return {
    [Role.Admin]: {
      id: 'admin',
      name: 'Admin',
      description: intl.formatMessage({ id: 'roles.admin_description' }),
      permissions: [
        {
          resource: RESOURCES.ALL,
          actions: [
            ACTIONS.CREATE as ActionType,
            ACTIONS.READ as ActionType,
            ACTIONS.UPDATE as ActionType,
            ACTIONS.DELETE as ActionType,
            ACTIONS.LIST as ActionType,
            ACTIONS.MANAGE as ActionType
          ]
        }
      ]
    },
    [Role.ClinicAdmin]: {
      id: 'clinic_admin',
      name: 'Clinic Admin',
      description: intl.formatMessage({ id: 'roles.clinic_admin_description' }),
      permissions: [
        {
          resource: RESOURCES.USERS,
          actions: [ACTIONS.READ as ActionType, ACTIONS.CREATE as ActionType, ACTIONS.UPDATE as ActionType, ACTIONS.LIST as ActionType]
        },
        {
          resource: RESOURCES.QUESTIONNAIRES,
          actions: [
            ACTIONS.READ as ActionType,
            ACTIONS.CREATE as ActionType,
            ACTIONS.UPDATE as ActionType,
            ACTIONS.DELETE as ActionType,
            ACTIONS.LIST as ActionType
          ]
        },
        {
          resource: RESOURCES.PATIENTS,
          actions: [
            ACTIONS.READ as ActionType,
            ACTIONS.CREATE as ActionType,
            ACTIONS.UPDATE as ActionType,
            ACTIONS.DELETE as ActionType,
            ACTIONS.LIST as ActionType
          ]
        },
        {
          resource: RESOURCES.REPORTS,
          actions: [ACTIONS.READ as ActionType, ACTIONS.CREATE as ActionType, ACTIONS.LIST as ActionType]
        },
        {
          resource: RESOURCES.CLINIC_SETTINGS,
          actions: [ACTIONS.READ as ActionType, ACTIONS.UPDATE as ActionType]
        }
      ]
    },
    [Role.Client]: {
      id: 'client',
      name: 'Client',
      description: intl.formatMessage({ id: 'roles.client_description' }),
      permissions: [
        {
          resource: RESOURCES.OWN_PROFILE,
          actions: [ACTIONS.READ as ActionType, ACTIONS.UPDATE as ActionType]
        },
        {
          resource: RESOURCES.PATIENTS,
          actions: [ACTIONS.READ as ActionType, ACTIONS.CREATE as ActionType, ACTIONS.LIST as ActionType]
        },
        {
          resource: RESOURCES.QUESTIONNAIRES,
          actions: [ACTIONS.READ as ActionType, ACTIONS.LIST as ActionType]
        }
      ]
    },
    [Role.Doctor]: {
      id: 'doctor',
      name: 'Doctor',
      description: intl.formatMessage({ id: 'roles.doctor_description' }),
      permissions: [
        {
          resource: RESOURCES.PATIENTS,
          actions: [ACTIONS.READ as ActionType, ACTIONS.CREATE as ActionType, ACTIONS.UPDATE as ActionType, ACTIONS.LIST as ActionType]
        },
        {
          resource: RESOURCES.QUESTIONNAIRES,
          actions: [ACTIONS.READ as ActionType, ACTIONS.CREATE as ActionType, ACTIONS.UPDATE as ActionType, ACTIONS.LIST as ActionType]
        },
        {
          resource: RESOURCES.REPORTS,
          actions: [ACTIONS.READ as ActionType, ACTIONS.CREATE as ActionType, ACTIONS.LIST as ActionType]
        }
      ]
    },
    [Role.Patient]: {
      id: 'patient',
      name: 'Patient',
      description: intl.formatMessage({ id: 'roles.patient_description' }),
      permissions: [
        {
          resource: RESOURCES.OWN_PROFILE,
          actions: [ACTIONS.READ as ActionType, ACTIONS.UPDATE as ActionType]
        },
        {
          resource: RESOURCES.OWN_QUESTIONNAIRES,
          actions: [ACTIONS.READ as ActionType, ACTIONS.CREATE as ActionType, ACTIONS.UPDATE as ActionType]
        },
        {
          resource: RESOURCES.OWN_REPORTS,
          actions: [ACTIONS.READ as ActionType, ACTIONS.LIST as ActionType]
        }
      ]
    }
  };
};

/**
 * Helper function to get translated resource names
 * Must be used within a component with React hooks
 */
export const useResourceDisplayNames = () => {
  const intl = useIntl();

  return {
    [RESOURCES.USERS]: intl.formatMessage({ id: 'resources.users' }),
    [RESOURCES.QUESTIONNAIRES]: intl.formatMessage({ id: 'resources.questionnaires' }),
    [RESOURCES.PATIENTS]: intl.formatMessage({ id: 'resources.patients' }),
    [RESOURCES.REPORTS]: intl.formatMessage({ id: 'resources.reports' }),
    [RESOURCES.CLINIC_SETTINGS]: intl.formatMessage({ id: 'resources.clinic_settings' }),
    [RESOURCES.SYSTEM_SETTINGS]: intl.formatMessage({ id: 'resources.system_settings' }),
    [RESOURCES.OWN_PROFILE]: intl.formatMessage({ id: 'resources.own_profile' }),
    [RESOURCES.OWN_QUESTIONNAIRES]: intl.formatMessage({ id: 'resources.own_questionnaires' }),
    [RESOURCES.OWN_REPORTS]: intl.formatMessage({ id: 'resources.own_reports' })
  };
};

/**
 * Helper function to get translated action names
 * Must be used within a component with React hooks
 */
export const useActionDisplayNames = () => {
  const intl = useIntl();

  return {
    [ACTIONS.CREATE]: intl.formatMessage({ id: 'actions.create' }),
    [ACTIONS.READ]: intl.formatMessage({ id: 'actions.read' }),
    [ACTIONS.UPDATE]: intl.formatMessage({ id: 'actions.update' }),
    [ACTIONS.DELETE]: intl.formatMessage({ id: 'actions.delete' }),
    [ACTIONS.LIST]: intl.formatMessage({ id: 'actions.list' }),
    [ACTIONS.MANAGE]: intl.formatMessage({ id: 'actions.manage' })
  };
};
