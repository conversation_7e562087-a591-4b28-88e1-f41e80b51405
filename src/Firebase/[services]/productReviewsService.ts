/**
 * Firebase Product Reviews Service
 *
 * This service provides methods for product review-related Firestore database operations.
 */
import { where, orderBy, limit, QueryConstraint } from 'firebase/firestore';
import { db } from '../[config]/firebase';
import {
  getDocById,
  queryDocuments,
  createDoc,
  createDocWithId,
  updateDocument,
  deleteDocument,
  upsertDoc,
  WithId
} from './firestoreUtils';

// Collection reference
const PRODUCT_REVIEWS_COLLECTION = 'productReviews';

/**
 * Get a product review by ID
 */
export const getProductReviewById = async <T>(id: string): Promise<WithId<T> | null> => {
  return getDocById<T>(PRODUCT_REVIEWS_COLLECTION, id);
};

/**
 * Get all product reviews with optional filtering
 */
export const getProductReviews = async <T>(
  queryParams: {
    userId?: string;
    productId?: string;
    rating?: number;
    minRating?: number;
    limit?: number;
    orderByField?: string;
    orderDirection?: 'asc' | 'desc';
  } = {}
): Promise<WithId<T>[]> => {
  const constraints: QueryConstraint[] = [];

  if (queryParams.userId) {
    constraints.push(where('userId', '==', queryParams.userId));
  }

  if (queryParams.productId) {
    constraints.push(where('productId', '==', queryParams.productId));
  }

  if (queryParams.rating !== undefined) {
    constraints.push(where('rating', '==', queryParams.rating));
  }

  if (queryParams.minRating !== undefined) {
    constraints.push(where('rating', '>=', queryParams.minRating));
  }

  if (queryParams.orderByField) {
    constraints.push(orderBy(queryParams.orderByField, queryParams.orderDirection || 'desc'));
  } else {
    // Default sorting by creation date
    constraints.push(orderBy('createdAt', 'desc'));
  }

  if (queryParams.limit) {
    constraints.push(limit(queryParams.limit));
  }

  return queryDocuments<T>(PRODUCT_REVIEWS_COLLECTION, constraints);
};

/**
 * Create a new product review with auto-generated ID
 */
export const createProductReview = async <T>(reviewData: T): Promise<WithId<T>> => {
  return createDoc<T>(PRODUCT_REVIEWS_COLLECTION, reviewData);
};

/**
 * Create a new product review with specific ID
 */
export const createProductReviewWithId = async <T>(id: string, reviewData: T): Promise<void> => {
  return createDocWithId<T>(PRODUCT_REVIEWS_COLLECTION, id, reviewData);
};

/**
 * Update a product review
 */
export const updateProductReview = async <T>(id: string, reviewData: Partial<T>): Promise<void> => {
  return updateDocument<T>(PRODUCT_REVIEWS_COLLECTION, id, reviewData);
};

/**
 * Delete a product review
 */
export const deleteProductReview = async (id: string): Promise<void> => {
  return deleteDocument(PRODUCT_REVIEWS_COLLECTION, id);
};

/**
 * Create or update a product review
 */
export const upsertProductReview = async <T>(id: string, reviewData: Partial<T>): Promise<void> => {
  return upsertDoc<T>(PRODUCT_REVIEWS_COLLECTION, id, reviewData);
};
