/**
 * Firebase Compliance Reports Service
 *
 * This service provides methods for compliance report-related Firestore database operations.
 */
import { where, orderBy, limit, QueryConstraint } from 'firebase/firestore';
import { db } from '../[config]/firebase';
import {
  getDocById,
  queryDocuments,
  createDoc,
  createDocWithId,
  updateDocument,
  deleteDocument,
  upsertDoc,
  WithId
} from './firestoreUtils';

// Collection reference
const COMPLIANCE_REPORTS_COLLECTION = 'compliance_reports';

/**
 * Get a compliance report by ID
 */
export const getComplianceReportById = async <T>(id: string): Promise<WithId<T> | null> => {
  return getDocById<T>(COMPLIANCE_REPORTS_COLLECTION, id);
};

/**
 * Get all compliance reports with optional filtering
 */
export const getComplianceReports = async <T>(
  queryParams: {
    patientId?: string;
    doctorId?: string;
    status?: string;
    limit?: number;
    orderByField?: string;
    orderDirection?: 'asc' | 'desc';
  } = {}
): Promise<WithId<T>[]> => {
  const constraints: QueryConstraint[] = [];

  if (queryParams.patientId) {
    constraints.push(where('patientId', '==', queryParams.patientId));
  }

  if (queryParams.doctorId) {
    constraints.push(where('doctorId', '==', queryParams.doctorId));
  }

  if (queryParams.status) {
    constraints.push(where('status', '==', queryParams.status));
  }

  if (queryParams.orderByField) {
    constraints.push(orderBy(queryParams.orderByField, queryParams.orderDirection || 'desc'));
  }

  if (queryParams.limit) {
    constraints.push(limit(queryParams.limit));
  }

  return queryDocuments<T>(COMPLIANCE_REPORTS_COLLECTION, constraints);
};

/**
 * Create a new compliance report with auto-generated ID
 */
export const createComplianceReport = async <T>(reportData: T): Promise<WithId<T>> => {
  return createDoc<T>(COMPLIANCE_REPORTS_COLLECTION, reportData);
};

/**
 * Create a new compliance report with specific ID
 */
export const createComplianceReportWithId = async <T>(id: string, reportData: T): Promise<void> => {
  return createDocWithId<T>(COMPLIANCE_REPORTS_COLLECTION, id, reportData);
};

/**
 * Update a compliance report
 */
export const updateComplianceReport = async <T>(id: string, reportData: Partial<T>): Promise<void> => {
  return updateDocument<T>(COMPLIANCE_REPORTS_COLLECTION, id, reportData);
};

/**
 * Delete a compliance report
 */
export const deleteComplianceReport = async (id: string): Promise<void> => {
  return deleteDocument(COMPLIANCE_REPORTS_COLLECTION, id);
};

/**
 * Create or update a compliance report
 */
export const upsertComplianceReport = async <T>(id: string, reportData: Partial<T>): Promise<void> => {
  return upsertDoc<T>(COMPLIANCE_REPORTS_COLLECTION, id, reportData);
};
