/**
 * Firebase Purchases Service
 *
 * This service provides methods for purchase-related Firestore database operations.
 */
import { where, orderBy, limit, QueryConstraint } from 'firebase/firestore';
import { db } from '../[config]/firebase';
import {
  getDocById,
  queryDocuments,
  createDoc,
  createDocWithId,
  updateDocument,
  deleteDocument,
  upsertDoc,
  WithId
} from './firestoreUtils';

// Collection reference
const PURCHASES_COLLECTION = 'purchases';

/**
 * Get a purchase by ID
 */
export const getPurchaseById = async <T>(id: string): Promise<WithId<T> | null> => {
  return getDocById<T>(PURCHASES_COLLECTION, id);
};

/**
 * Get all purchases with optional filtering
 */
export const getPurchases = async <T>(
  queryParams: {
    userId?: string;
    status?: string;
    productId?: string;
    limit?: number;
    orderByField?: string;
    orderDirection?: 'asc' | 'desc';
  } = {}
): Promise<WithId<T>[]> => {
  const constraints: QueryConstraint[] = [];

  if (queryParams.userId) {
    constraints.push(where('userId', '==', queryParams.userId));
  }

  if (queryParams.status) {
    constraints.push(where('status', '==', queryParams.status));
  }

  if (queryParams.productId) {
    constraints.push(where('productId', '==', queryParams.productId));
  }

  if (queryParams.orderByField) {
    constraints.push(orderBy(queryParams.orderByField, queryParams.orderDirection || 'desc'));
  }

  if (queryParams.limit) {
    constraints.push(limit(queryParams.limit));
  }

  return queryDocuments<T>(PURCHASES_COLLECTION, constraints);
};

/**
 * Create a new purchase with auto-generated ID
 */
export const createPurchase = async <T>(purchaseData: T): Promise<WithId<T>> => {
  return createDoc<T>(PURCHASES_COLLECTION, purchaseData);
};

/**
 * Create a new purchase with specific ID
 */
export const createPurchaseWithId = async <T>(id: string, purchaseData: T): Promise<void> => {
  return createDocWithId<T>(PURCHASES_COLLECTION, id, purchaseData);
};

/**
 * Update a purchase
 */
export const updatePurchase = async <T>(id: string, purchaseData: Partial<T>): Promise<void> => {
  return updateDocument<T>(PURCHASES_COLLECTION, id, purchaseData);
};

/**
 * Delete a purchase
 */
export const deletePurchase = async (id: string): Promise<void> => {
  return deleteDocument(PURCHASES_COLLECTION, id);
};

/**
 * Create or update a purchase
 */
export const upsertPurchase = async <T>(id: string, purchaseData: Partial<T>): Promise<void> => {
  return upsertDoc<T>(PURCHASES_COLLECTION, id, purchaseData);
};

/**
 * Complete a purchase
 */
export const completePurchase = async <T>(id: string, completionData: Partial<T>): Promise<void> => {
  return updateDocument<T>(PURCHASES_COLLECTION, id, {
    ...completionData,
    status: 'completed'
  });
};
