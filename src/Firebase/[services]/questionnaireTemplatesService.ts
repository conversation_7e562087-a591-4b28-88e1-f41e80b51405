/**
 * Firebase Questionnaire Templates Service
 *
 * This service provides methods for questionnaire template-related Firestore database operations.
 */
import { where, orderBy, limit, QueryConstraint } from 'firebase/firestore';
import { db } from '../[config]/firebase';
import {
  getDocById,
  queryDocuments,
  createDoc,
  createDocWithId,
  updateDocument,
  deleteDocument,
  upsertDoc,
  WithId
} from './firestoreUtils';

// Collection reference
const QUESTIONNAIRE_TEMPLATES_COLLECTION = 'questionnaireTemplates';

/**
 * Get a questionnaire template by ID
 */
export const getQuestionnaireTemplateById = async <T>(id: string): Promise<WithId<T> | null> => {
  return getDocById<T>(QUESTIONNAIRE_TEMPLATES_COLLECTION, id);
};

/**
 * Get all questionnaire templates with optional filtering
 */
export const getQuestionnaireTemplates = async <T>(
  queryParams: {
    category?: string;
    active?: boolean;
    limit?: number;
    orderByField?: string;
    orderDirection?: 'asc' | 'desc';
  } = {}
): Promise<WithId<T>[]> => {
  const constraints: QueryConstraint[] = [];

  if (queryParams.category) {
    constraints.push(where('category', '==', queryParams.category));
  }

  if (queryParams.active !== undefined) {
    constraints.push(where('active', '==', queryParams.active));
  }

  if (queryParams.orderByField) {
    constraints.push(orderBy(queryParams.orderByField, queryParams.orderDirection || 'desc'));
  }

  if (queryParams.limit) {
    constraints.push(limit(queryParams.limit));
  }

  return queryDocuments<T>(QUESTIONNAIRE_TEMPLATES_COLLECTION, constraints);
};

/**
 * Create a new questionnaire template with auto-generated ID
 */
export const createQuestionnaireTemplate = async <T>(templateData: T): Promise<WithId<T>> => {
  return createDoc<T>(QUESTIONNAIRE_TEMPLATES_COLLECTION, templateData);
};

/**
 * Create a new questionnaire template with specific ID
 */
export const createQuestionnaireTemplateWithId = async <T>(id: string, templateData: T): Promise<void> => {
  return createDocWithId<T>(QUESTIONNAIRE_TEMPLATES_COLLECTION, id, templateData);
};

/**
 * Update a questionnaire template
 */
export const updateQuestionnaireTemplate = async <T>(id: string, templateData: Partial<T>): Promise<void> => {
  return updateDocument<T>(QUESTIONNAIRE_TEMPLATES_COLLECTION, id, templateData);
};

/**
 * Delete a questionnaire template
 */
export const deleteQuestionnaireTemplate = async (id: string): Promise<void> => {
  return deleteDocument(QUESTIONNAIRE_TEMPLATES_COLLECTION, id);
};

/**
 * Create or update a questionnaire template
 */
export const upsertQuestionnaireTemplate = async <T>(id: string, templateData: Partial<T>): Promise<void> => {
  return upsertDoc<T>(QUESTIONNAIRE_TEMPLATES_COLLECTION, id, templateData);
};
