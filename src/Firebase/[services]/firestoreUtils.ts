/**
 * Firestore Utilities
 *
 * This file re-exports functions from firestore.ts for better organization
 * and to maintain consistent imports across services.
 */

import {
  convertDoc,
  convertDocs,
  getDocById,
  createDoc,
  upsertDoc,
  updateDoc2 as updateDocument,
  deleteDocument,
  queryDocuments,
  getBatch
} from './firestore';
import { doc, setDoc, serverTimestamp } from 'firebase/firestore';
import { db } from '../[config]/firebase';

// Re-export type with proper syntax for isolated modules
export type { WithId } from './firestore';

// Re-export functions from firestore.ts
export { convertDoc, convertDocs, getDocById, createDoc, upsertDoc, updateDocument, deleteDocument, queryDocuments, getBatch };

/**
 * Create a new document with specific ID
 * This function was missing from firestore.ts
 */
export const createDocWithId = async <T>(collectionPath: string, id: string, data: T): Promise<void> => {
  const docRef = doc(db, collectionPath, id);
  await setDoc(docRef, {
    ...data,
    createdAt: serverTimestamp(),
    updatedAt: serverTimestamp()
  });
};
