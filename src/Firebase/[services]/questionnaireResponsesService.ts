/**
 * Firebase Questionnaire Responses Service
 *
 * This service provides methods for questionnaire response-related Firestore database operations.
 */
import { where, orderBy, limit, QueryConstraint } from 'firebase/firestore';
import { db } from '../[config]/firebase';
import {
  getDocById,
  queryDocuments,
  createDoc,
  createDocWithId,
  updateDocument,
  deleteDocument,
  upsertDoc,
  WithId
} from './firestoreUtils';

// Collection reference
const QUESTIONNAIRE_RESPONSES_COLLECTION = 'questionnaireResponses';

/**
 * Get a questionnaire response by ID
 */
export const getQuestionnaireResponseById = async <T>(id: string): Promise<WithId<T> | null> => {
  return getDocById<T>(QUESTIONNAIRE_RESPONSES_COLLECTION, id);
};

/**
 * Get all questionnaire responses with optional filtering
 */
export const getQuestionnaireResponses = async <T>(
  queryParams: {
    questionnaireId?: string;
    patientId?: string;
    limit?: number;
    orderByField?: string;
    orderDirection?: 'asc' | 'desc';
  } = {}
): Promise<WithId<T>[]> => {
  const constraints: QueryConstraint[] = [];

  if (queryParams.questionnaireId) {
    constraints.push(where('questionnaireId', '==', queryParams.questionnaireId));
  }

  if (queryParams.patientId) {
    constraints.push(where('patientId', '==', queryParams.patientId));
  }

  if (queryParams.orderByField) {
    constraints.push(orderBy(queryParams.orderByField, queryParams.orderDirection || 'desc'));
  }

  if (queryParams.limit) {
    constraints.push(limit(queryParams.limit));
  }

  return queryDocuments<T>(QUESTIONNAIRE_RESPONSES_COLLECTION, constraints);
};

/**
 * Create a new questionnaire response with auto-generated ID
 */
export const createQuestionnaireResponse = async <T>(responseData: T): Promise<WithId<T>> => {
  return createDoc<T>(QUESTIONNAIRE_RESPONSES_COLLECTION, responseData);
};

/**
 * Create a new questionnaire response with specific ID
 */
export const createQuestionnaireResponseWithId = async <T>(id: string, responseData: T): Promise<void> => {
  return createDocWithId<T>(QUESTIONNAIRE_RESPONSES_COLLECTION, id, responseData);
};

/**
 * Update a questionnaire response
 */
export const updateQuestionnaireResponse = async <T>(id: string, responseData: Partial<T>): Promise<void> => {
  return updateDocument<T>(QUESTIONNAIRE_RESPONSES_COLLECTION, id, responseData);
};

/**
 * Delete a questionnaire response
 */
export const deleteQuestionnaireResponse = async (id: string): Promise<void> => {
  return deleteDocument(QUESTIONNAIRE_RESPONSES_COLLECTION, id);
};

/**
 * Create or update a questionnaire response
 */
export const upsertQuestionnaireResponse = async <T>(id: string, responseData: Partial<T>): Promise<void> => {
  return upsertDoc<T>(QUESTIONNAIRE_RESPONSES_COLLECTION, id, responseData);
};
