/**
 * Firebase Reports Service
 *
 * This service provides methods for report-related Firestore database operations.
 */
import { where, orderBy, limit, QueryConstraint } from 'firebase/firestore';
import { db } from '../[config]/firebase';
import {
  getDocById,
  queryDocuments,
  createDoc,
  createDocWithId,
  updateDocument,
  deleteDocument,
  upsertDoc,
  WithId
} from './firestoreUtils';

// Collection reference
const REPORTS_COLLECTION = 'reports';

/**
 * Get a report by ID
 */
export const getReportById = async <T>(id: string): Promise<WithId<T> | null> => {
  return getDocById<T>(REPORTS_COLLECTION, id);
};

/**
 * Get all reports with optional filtering
 */
export const getReports = async <T>(
  queryParams: {
    patientId?: string;
    doctorId?: string;
    status?: string;
    limit?: number;
    orderByField?: string;
    orderDirection?: 'asc' | 'desc';
  } = {}
): Promise<WithId<T>[]> => {
  const constraints: QueryConstraint[] = [];

  if (queryParams.patientId) {
    constraints.push(where('patientId', '==', queryParams.patientId));
  }

  if (queryParams.doctorId) {
    constraints.push(where('doctorId', '==', queryParams.doctorId));
  }

  if (queryParams.status) {
    constraints.push(where('status', '==', queryParams.status));
  }

  if (queryParams.orderByField) {
    constraints.push(orderBy(queryParams.orderByField, queryParams.orderDirection || 'desc'));
  }

  if (queryParams.limit) {
    constraints.push(limit(queryParams.limit));
  }

  return queryDocuments<T>(REPORTS_COLLECTION, constraints);
};

/**
 * Create a new report with auto-generated ID
 */
export const createReport = async <T>(reportData: T): Promise<WithId<T>> => {
  return createDoc<T>(REPORTS_COLLECTION, reportData);
};

/**
 * Create a new report with specific ID
 */
export const createReportWithId = async <T>(id: string, reportData: T): Promise<void> => {
  return createDocWithId<T>(REPORTS_COLLECTION, id, reportData);
};

/**
 * Update a report
 */
export const updateReport = async <T>(id: string, reportData: Partial<T>): Promise<void> => {
  return updateDocument<T>(REPORTS_COLLECTION, id, reportData);
};

/**
 * Delete a report
 */
export const deleteReport = async (id: string): Promise<void> => {
  return deleteDocument(REPORTS_COLLECTION, id);
};

/**
 * Create or update a report
 */
export const upsertReport = async <T>(id: string, reportData: Partial<T>): Promise<void> => {
  return upsertDoc<T>(REPORTS_COLLECTION, id, reportData);
};
