/**
 * Firebase Configuration
 *
 * This file contains the Firebase initialization and service exports.
 * All Firebase interactions should use these instances.
 */
import { initializeApp } from 'firebase/app';
import { getAuth } from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';
import { getStorage } from 'firebase/storage';
import { getAnalytics, isSupported } from 'firebase/analytics';

// Firebase configuration
const firebaseConfig = {
  apiKey: (import.meta.env && import.meta.env.VITE_APP_FIREBASE_API_KEY) || process.env.VITE_APP_FIREBASE_API_KEY,
  authDomain: (import.meta.env && import.meta.env.VITE_APP_FIREBASE_AUTH_DOMAIN) || process.env.VITE_APP_FIREBASE_AUTH_DOMAIN,
  projectId: (import.meta.env && import.meta.env.VITE_APP_FIREBASE_PROJECT_ID) || process.env.VITE_APP_FIREBASE_PROJECT_ID,
  storageBucket: (import.meta.env && import.meta.env.VITE_APP_FIREBASE_STORAGE_BUCKET) || process.env.VITE_APP_FIREBASE_STORAGE_BUCKET,
  messagingSenderId:
    (import.meta.env && import.meta.env.VITE_APP_FIREBASE_MESSAGING_SENDER_ID) || process.env.VITE_APP_FIREBASE_MESSAGING_SENDER_ID,
  appId: (import.meta.env && import.meta.env.VITE_APP_FIREBASE_APP_ID) || process.env.VITE_APP_FIREBASE_APP_ID,
  measurementId: (import.meta.env && import.meta.env.VITE_APP_FIREBASE_MEASUREMENT_ID) || process.env.VITE_APP_FIREBASE_MEASUREMENT_ID
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase services
export const auth = getAuth(app);
export const db = getFirestore(app);
export const storage = getStorage(app);

// Initialize Analytics if supported
let analytics;
isSupported().then((supported) => {
  if (supported) {
    analytics = getAnalytics(app);
    console.log('Firebase Analytics initialized successfully.');
  } else {
    console.log('Firebase Analytics is not supported in this environment.');
  }
});

export { analytics };
export default app;
