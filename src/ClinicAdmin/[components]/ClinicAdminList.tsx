import React, { useEffect, useState } from 'react';
import {
  List,
  ListItemAvatar,
  ListItemText,
  Avatar,
  Checkbox,
  CircularProgress,
  Typography,
  Box,
  Divider,
  ListItemButton
} from '@mui/material';
import { getUsers } from 'Users/[services]/userService';
import { TRQUser } from 'Users/[types]/User';
import { Role } from '../../RBAC/[types]/Role';

interface ClinicAdminListProps {
  selectedAdminIds: string[];
  onSelectionChange: (ids: string[]) => void;
  disabledAdminIds?: string[];
  clinicId?: string;
}

const ClinicAdminList: React.FC<ClinicAdminListProps> = ({ selectedAdminIds, onSelectionChange, disabledAdminIds = [], clinicId }) => {
  const [admins, setAdmins] = useState<TRQUser[]>([]);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    const fetchAdmins = async () => {
      setLoading(true);
      try {
        const adminUsers = await getUsers({
          role: Role.ClinicAdmin,
          isActive: true,
          ...(clinicId ? { clinicId } : {})
        });
        setAdmins(adminUsers);
      } catch {
        setAdmins([]);
      } finally {
        setLoading(false);
      }
    };
    fetchAdmins();
  }, [clinicId]);

  const handleToggle = (id: string) => {
    if (disabledAdminIds.includes(id)) return;
    if (selectedAdminIds.includes(id)) {
      onSelectionChange(selectedAdminIds.filter((aid) => aid !== id));
    } else {
      onSelectionChange([...selectedAdminIds, id]);
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 120 }}>
        <CircularProgress size={28} />
      </Box>
    );
  }

  if (admins.length === 0) {
    return (
      <Typography variant="body1" sx={{ p: 2 }}>
        No clinic administrators found.
      </Typography>
    );
  }

  return (
    <List>
      {admins.map((admin, idx) => (
        <React.Fragment key={admin.id}>
          <ListItemButton
            alignItems="flex-start"
            onClick={() => admin.id && handleToggle(admin.id)}
            disabled={!!admin.id && disabledAdminIds.includes(admin.id!)}
          >
            <ListItemAvatar>
              <Avatar>
                {admin.firstName?.[0] || ''}
                {admin.lastName?.[0] || ''}
              </Avatar>
            </ListItemAvatar>
            <ListItemText
              primary={`${admin.firstName} ${admin.lastName}${admin.title ? ` (${admin.title})` : ''}`}
              secondary={
                <>
                  {admin.email}
                  {admin.phone ? ` • ${admin.phone}` : ''}
                </>
              }
            />
            <Checkbox
              edge="end"
              checked={!!admin.id && selectedAdminIds.includes(admin.id!)}
              onChange={(e) => {
                e.stopPropagation();
                admin.id && handleToggle(admin.id);
              }}
              disabled={!!admin.id && disabledAdminIds.includes(admin.id!)}
              tabIndex={-1}
            />
          </ListItemButton>
          {idx < admins.length - 1 && <Divider variant="inset" component="li" />}
        </React.Fragment>
      ))}
    </List>
  );
};

export default ClinicAdminList;
