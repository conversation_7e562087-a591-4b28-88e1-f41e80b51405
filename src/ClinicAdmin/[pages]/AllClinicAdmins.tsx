import React, { useState, useEffect, useMemo } from 'react';
import { useIntl } from 'react-intl';
import { useNavigate } from 'react-router-dom';
import { GridColDef, GridRowParams } from '@mui/x-data-grid';
import { Box, CircularProgress, Typography, Grid, useTheme } from '@mui/material';

// project imports
import MainCard from '[components]/cards/MainCard';
import ActionToolbar from '[components]/trq/shared/ActionToolbar';
import { getUsers, deleteUser } from '../../Users/<USER>/userService'; // Corrected path
import { TRQUser } from '../../Users/<USER>/User'; // Corrected path
import { Role } from 'RBAC/[types]/Role';
import ConfirmDeleteDialog from '[components]/trq/shared/ConfirmDeleteDialog';
import usePermission from 'RBAC/[hooks]/usePermission';
import TrqDataGrid from '[components]/trq/TrqDataGrid';
import AdminPanelSettingsIcon from '@mui/icons-material/AdminPanelSettings';
import PersonAddIcon from '@mui/icons-material/PersonAdd';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import LocalHospitalIcon from '@mui/icons-material/LocalHospital';
import ROUTES from 'Routing/appRoutes';
import StatsInfoTile from '[components]/cards/StatsInfoTile';

const AllClinicAdmins = () => {
  const navigate = useNavigate();
  const intl = useIntl();
  const permissions = usePermission();
  const [admins, setAdmins] = useState<TRQUser[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [selectedRows, setSelectedRows] = useState<string[]>([]);
  const [isConfirmDeleteDialogOpen, setIsConfirmDeleteDialogOpen] = useState<boolean>(false);
  const [itemsToDeleteIds, setItemsToDeleteIds] = useState<string[] | null>(null);
  const [isDeleting, setIsDeleting] = useState<boolean>(false);
  const theme = useTheme();

  // Memoize the permission value so the effect doesn't loop
  const canList = useMemo(() => permissions.checkPermission('users', 'list'), [permissions]);

  useEffect(() => {
    if (!canList) {
      setLoading(false);
      return;
    }
    const fetchAdmins = async () => {
      setLoading(true);
      try {
        // Fetch users with role ClinicAdmin
        // Type assertion to fix TypeScript error
        const adminsData = await getUsers({ role: Role.ClinicAdmin as Role });
        setAdmins(adminsData);
      } catch (error) {
        console.error('Error fetching clinic admins:', error);
        // TODO: Add user notification for errors
      } finally {
        setLoading(false);
      }
    };
    fetchAdmins();
  }, [canList]);

  // Update the statsData calculation
  const statsData = useMemo(() => {
    // Get the count of unique clinics
    const uniqueClinics = new Set(admins.filter((admin) => admin?.asClinicAdmin?.clinicId).map((admin) => admin.asClinicAdmin?.clinicId))
      .size;

    // Calculate new admins in the last 30 days
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const recentAdmins = admins.filter((a) => {
      if (!a.createdAt) return false;
      const created = a.createdAt?.toDate ? a.createdAt.toDate() : new Date(a.createdAt as any);
      return created >= thirtyDaysAgo;
    }).length;

    return [
      {
        title: 'Total Administrators',
        count: admins.length,
        icon: <AdminPanelSettingsIcon fontSize="large" />,
        color: theme.palette.primary.main
      },
      {
        title: 'Active Administrators',
        count: admins.filter((a) => a.isActive).length,
        icon: <CheckCircleIcon fontSize="large" />,
        color: theme.palette.success.main
      },
      {
        title: 'Unique Clinics',
        count: uniqueClinics,
        icon: <LocalHospitalIcon fontSize="large" />,
        color: theme.palette.info.main
      },
      {
        title: 'New in Last 30 Days',
        count: recentAdmins,
        icon: <PersonAddIcon fontSize="large" />,
        color: theme.palette.warning.main
      }
    ];
  }, [admins, theme.palette]);

  // Columns definition
  const columns: GridColDef<TRQUser>[] = [
    {
      field: 'name',
      headerName: intl.formatMessage({ id: 'name' }) || 'Name',
      flex: 1.5,
      minWidth: 180,
      valueGetter: (_, row) => {
        return row ? `${row.firstName || ''} ${row.lastName || ''}`.trim() : '';
      }
    },
    {
      field: 'email',
      headerName: intl.formatMessage({ id: 'email' }) || 'Email',
      flex: 1.5,
      minWidth: 200,
      valueGetter: (_, row) => {
        return row ? row.email || '-' : '-';
      }
    },
    {
      field: 'phone',
      headerName: intl.formatMessage({ id: 'phone' }) || 'Phone',
      flex: 1,
      minWidth: 150,
      valueGetter: (_, row) => {
        return row?.phone || '-';
      }
    },
    {
      field: 'clinic',
      headerName: intl.formatMessage({ id: 'clinic' }) || 'Clinic',
      flex: 1.5,
      minWidth: 180,
      valueGetter: (_, row) => {
        if (!row?.asClinicAdmin?.clinicId) return '-';
        // Since clinicName might not be available directly, just show the clinic ID
        // In a real implementation, you might want to fetch clinic details separately
        return `Clinic ID: ${row?.asClinicAdmin?.clinicId}` || 'Unknown Clinic';
      }
    }
  ];

  const handleViewAdmin = (adminId: string) => {
    if (permissions.checkPermission('users', 'read')) {
      navigate(ROUTES.CLINIC_ADMINS.DETAILS(adminId));
    }
  };

  const handleEditAdmin = (adminId: string) => {
    if (permissions.checkPermission('users', 'update')) {
      navigate(ROUTES.CLINIC_ADMINS.EDIT(adminId));
    }
  };

  const handleDeleteAdmin = async (uids: string[]) => {
    if (!uids || uids.length === 0 || !permissions.checkPermission('users', 'delete')) return;
    setItemsToDeleteIds(uids);
    setIsConfirmDeleteDialogOpen(true);
  };

  const executeDelete = async () => {
    if (!itemsToDeleteIds || itemsToDeleteIds.length === 0 || !permissions.checkPermission('users', 'delete')) return;
    setIsDeleting(true);
    try {
      await Promise.all(itemsToDeleteIds.map((id) => deleteUser(id)));

      setAdmins((prevAdmins) => prevAdmins.filter((admin) => !itemsToDeleteIds.includes(admin.uid)));
      setSelectedRows((prevSelected) => prevSelected.filter((id) => !itemsToDeleteIds.includes(id)));
    } catch (error) {
      console.error('Error deleting clinic admin(s):', error);
      // TODO: Show error notification to user
    } finally {
      setIsConfirmDeleteDialogOpen(false);
      setItemsToDeleteIds(null);
      setIsDeleting(false);
    }
  };

  const handleRowClick = (params: GridRowParams) => {
    if (permissions.checkPermission('users', 'read')) {
      navigate(ROUTES.CLINIC_ADMINS.DETAILS(params.id as string));
    }
  };

  const handleAddAdmin = () => {
    if (permissions.checkPermission('users', 'create')) {
      navigate(ROUTES.CLINIC_ADMINS.ADD);
    }
  };

  if (!permissions.checkPermission('users', 'list')) {
    return (
      <MainCard>
        <Typography variant="h3" color="error" align="center">
          {intl.formatMessage({ id: 'no-permission' }) || 'You do not have permission to view clinic administrators'}
        </Typography>
      </MainCard>
    );
  }

  return (
    <MainCard>
      {/* Update the statistics section to use StatsInfoTile */}
      <Box sx={{ mb: 4, mt: 1 }}>
        <Typography variant="h4" sx={{ mb: 3, fontWeight: 600 }}>
          {intl.formatMessage({ id: 'clinic-admin-dashboard' }) || 'Clinic Administrator Dashboard'}
        </Typography>
        <Grid container spacing={3}>
          {statsData.map((stat, idx) => (
            <Grid item xs={12} sm={6} md={3} key={idx}>
              <StatsInfoTile title={stat.title} count={stat.count} icon={stat.icon} color={stat.color} variant="pattern" index={idx} />
            </Grid>
          ))}
        </Grid>
      </Box>

      <Box sx={{ height: 'calc(100vh - 240px)', width: '100%' }}>
        {loading ? (
          <Box display="flex" justifyContent="center" alignItems="center" height="100%">
            <CircularProgress />
          </Box>
        ) : (
          <TrqDataGrid
            rows={admins}
            getRowId={(row) => row.uid}
            columns={columns}
            checkboxSelection
            disableRowSelectionOnClick
            onRowSelectionModelChange={(newSelection) => {
              setSelectedRows(newSelection as string[]);
            }}
            rowSelectionModel={selectedRows}
            onRowClick={handleRowClick}
            slots={{
              toolbar: () => (
                <ActionToolbar
                  title={intl.formatMessage({ id: 'all-clinic-admins' }) || 'All Clinic Administrators'}
                  selectedRows={selectedRows}
                  addLabelContext={intl.formatMessage({ id: 'clinic-admin' }) || 'Clinic Admin'}
                  onAdd={permissions.checkPermission('users', 'create') ? handleAddAdmin : () => {}}
                  onView={permissions.checkPermission('users', 'read') ? handleViewAdmin : () => {}}
                  onEdit={permissions.checkPermission('users', 'update') ? handleEditAdmin : () => {}}
                  onDelete={permissions.checkPermission('users', 'delete') ? (ids) => handleDeleteAdmin(ids) : () => {}}
                />
              )
            }}
            initialState={{
              pagination: {
                paginationModel: { page: 0, pageSize: 10 }
              }
            }}
            pageSizeOptions={[5, 10, 25]}
          />
        )}
      </Box>

      {permissions.checkPermission('users', 'delete') && (
        <ConfirmDeleteDialog
          open={isConfirmDeleteDialogOpen}
          onClose={() => setIsConfirmDeleteDialogOpen(false)}
          onConfirm={executeDelete}
          title={intl.formatMessage({ id: 'confirm-delete' }) || 'Confirm Delete'}
          contentText={
            itemsToDeleteIds && itemsToDeleteIds.length > 1
              ? intl.formatMessage({ id: 'delete-multiple-admins-confirmation' }, { count: itemsToDeleteIds.length }) ||
                `Are you sure you want to delete ${itemsToDeleteIds.length} selected clinic administrators? This action cannot be undone.`
              : intl.formatMessage(
                  { id: 'delete-admin-confirmation' },
                  {
                    admin: admins.find((p) => p.uid === (itemsToDeleteIds?.[0] || ''))
                      ? `${admins.find((p) => p.uid === (itemsToDeleteIds?.[0] || ''))?.firstName} ${admins.find((p) => p.uid === (itemsToDeleteIds?.[0] || ''))?.lastName}`
                      : intl.formatMessage({ id: 'this-admin' }) || 'this clinic administrator'
                  }
                ) || `Are you sure you want to delete this clinic administrator? This action cannot be undone.`
          }
          isLoading={isDeleting}
        />
      )}
    </MainCard>
  );
};

export default AllClinicAdmins;
