{"dashboard": "Panel de Administración", "default": "Predeterminado", "analytics": "<PERSON><PERSON><PERSON><PERSON>", "widget": "Widget", "statistics": "Estadísticas", "data": "Datos", "chart": "Gráfico", "application": "Aplicación", "users": "Usuarios", "user": "Usuario", "posts": "Publicaciones", "social-profile": "Perfil social", "account-profile": "Perfil de cuenta", "follower": "<PERSON><PERSON><PERSON><PERSON>", "friends": "Amigos", "gallery": "Galería", "friend-request": "Solicitud de amistad", "profile": "Perfil", "profile 01": "Perfil 01", "profile 02": "Perfil 02", "profile 03": "Perfil 03", "cards": "Tarjetas", "list": "Lista", "create": "<PERSON><PERSON><PERSON>", "order-details": "Detalles del pedido", "style": "<PERSON><PERSON><PERSON>", "style 01": "Estilo 01", "style 02": "Estilo 02", "style 03": "Estilo 03", "customer": "Cliente", "customer-list": "Lista de clientes", "order-list": "Lista de pedidos", "create-invoice": "<PERSON><PERSON><PERSON> factura", "product": "Producto", "product-review": "Revisión del producto", "chat": "Cha<PERSON>", "mail": "<PERSON><PERSON><PERSON>", "contact": "Contacto", "calendar": "Calendario", "kanban": "Ka<PERSON><PERSON>", "board": "<PERSON><PERSON>", "backlogs": "Pendientes", "taskboard": "<PERSON>ro de tareas", "e-commerce": "Comercio electrónico", "products": "Productos", "product-details": "Detalles del producto", "product-list": "Lista de productos", "checkout": "<PERSON><PERSON>", "invoice": "Factura", "client": "Cliente", "item": "<PERSON><PERSON><PERSON><PERSON>", "payment": "Pago", "details": "Detalles", "edit": "<PERSON><PERSON>", "crm": "CRM", "lead-management": "Gestión de leads", "loading": "Cargando", "overview": "Resumen", "lead-list": "<PERSON><PERSON>", "contact-management": "Gestión de contactos", "contact-card": "Tarjeta de contacto", "contact-list": "Lista de contactos", "reminders-followup": "Recordatorios y seguimiento", "communication-history": "Historial de comunicación", "sales-management": "Gestión de ventas", "statement": "Declaración", "refund": "Reembolso", "earning": "Ganancia", "blog": "Blog", "general-settings": "Configuración general", "blog-list": "Lista de blogs", "blog-details": "Detalles del blog", "add-new": "Agregar nuevo", "forms": "Formularios", "components": {"AddTeamMemberModal": {"form": {"role": {"admin": "Administrador", "superAdmin": "Super Administrador"}}}}, "autocomplete": "Autocompletar", "button": {"go-back": "Volver", "go-home": "<PERSON><PERSON> al panel"}, "checkbox": "Casilla de verificación", "date-time": "<PERSON><PERSON> y hora", "radio": "Radio", "slider": "<PERSON><PERSON><PERSON><PERSON>", "switch": "Interruptor", "text-field": "Campo de texto", "plugins": "Complementos", "mask": "Máscara", "clipboard": "Portapapeles", "recaptcha": "reCaptcha", "wysiwug-editor": "Editor WYSIWYG", "modal": "Modal", "tooltip": "Información sobre herramientas", "dropzone": "Zona de arrastre", "table": "Tabla", "table-basic": "Tabla básica", "table-dense": "Tabla densa", "table-enhanced": "Tablas mejoradas", "table-data": "Tabla de datos", "table-customized": "Tabla personalizada", "table-sticky-header": "Encabezado fijo", "table-collapse": "Tabla colapsable", "data-grid": "Cuadrícula de datos", "data-grid-basic": "Básico", "data-grid-inline-editing": "Edición en línea", "data-grid-column-groups": "Grupos de columnas", "data-grid-save-restore": "Guardar y restaurar", "data-grid-quick-filter": "Filtro rápido", "data-grid-column-visibility": "Visibilidad de columnas", "data-grid-column-virtualization": "Virtualización de columnas", "data-grid-column-menu": "Menú de columnas", "charts": "Grá<PERSON><PERSON>", "apexchart": "Gráfico Apex", "organization-chart": "Organigrama", "forms-validation": "Validación de formularios", "forms-wizard": "Asistente de formularios", "layouts": "Diseños", "multi-column-forms": "Formularios de múltiples columnas", "action-bar": "Barra de acciones", "sticky-action-bar": "Barra de acciones fija", "ui-element": "Elemento de UI", "basic": "Básico", "basic-caption": "8+ Componentes básicos", "accordion": "Acordeón", "avatar": "Avatar", "badges": "<PERSON><PERSON><PERSON>", "breadcrumb": "Migas de pan", "chip": "Chip", "tabs": "Pestañas", "advance": "<PERSON><PERSON><PERSON>", "alert": "<PERSON><PERSON><PERSON>", "dialog": "Diálogo", "pagination": "Paginación", "progress": "Progreso", "rating": "Calificación", "snackbar": "Barra de notificaciones", "skeleton": "Esqueleto", "speeddial": "Marcac<PERSON>", "timeline": "Línea de tiempo", "toggle-button": "Botón de alternancia", "treeview": "Vista de árbol", "pages": "<PERSON><PERSON><PERSON><PERSON>", "pages-caption": "Páginas preconstruidas", "authentication": "Autenticación", "authentication 1": "Autenticación 1", "authentication 2": "Autenticación 2", "authentication 3": "Autenticación 3", "login": "In<PERSON><PERSON>", "register": "Registrarse", "forgot-password": "Olvid<PERSON> mi contraseña", "check-mail": "<PERSON><PERSON><PERSON> co<PERSON>o", "reset-password": "Restablecer contraseña", "code-verification": "Verificación de código", "pricing": "<PERSON><PERSON><PERSON>", "price": "Precio", "price 01": "Precio 01", "price 02": "Precio 02", "maintenance": "Mantenimiento", "map": "Mapa", "error-404": "Error 404", "error-500": "Error 500", "coming-soon": "Próximamente", "coming-soon 01": "Próximamente 01", "coming-soon 02": "Próximamente 02", "under-construction": "En construcción", "landing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "contact-us": "Cont<PERSON><PERSON><PERSON>s", "faqs": "Preguntas frecuentes", "privacy-policy": "Política de privacidad", "utilities": "Utilidades", "typography": "Tipografía", "color": "Color", "shadow": "Sombra", "icons": "Iconos", "tabler-icons": "Iconos de Tabler", "material-icons": "Iconos de Material", "animation": "Animación", "grid": "Cuadrícula", "others": "<PERSON><PERSON><PERSON>", "menu-level": "<PERSON><PERSON><PERSON> de <PERSON>ú", "level": "<PERSON><PERSON>", "level 1": "Nivel 1", "level 2": "Nivel 2", "level 3": "Nivel 3", "menu-level-subtitle": "Niveles de subtítulo", "menu-level-subtitle-caption": "<PERSON><PERSON><PERSON><PERSON><PERSON> contraer", "menu-level-subtitle-item": "Elemento de subtítulo", "menu-level-subtitle-collapse": "Sub contraer subtítulo", "menu-level-subtitle-sub-item": "Sub elemento de subtítulo", "disabled-menu": "<PERSON><PERSON> deshabilitado", "oval-chip-menu": "Menú de chip ovalado", "coded": "Codificado", "c": "C", "outlined": "<PERSON><PERSON><PERSON><PERSON>", "sample-page": "Página de ejemplo", "documentation": "Documentación", "roadmap": "Hoja de ruta", "title": "Idioma m<PERSON>", "home": "<PERSON><PERSON>o", "change": "Cambiar idioma", "message": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.", "account-settings": "Configuración de cuenta", "logout": "<PERSON><PERSON><PERSON>", "more-items": "Más element<PERSON>", "all-users": "Todos los usuarios", "user-details": "Detalles del usuario", "back-to-users": "Volver a usuarios", "add-user": "Agregar usuario", "add-new-user": "Agregar nuevo usuario", "add-user-description": "Complete los detalles para agregar un nuevo usuario al sistema.", "view": "<PERSON>er", "delete": "Eliminar", "cancel": "<PERSON><PERSON><PERSON>", "add": "Agregar", "save": "Guardar", "name": "Nombre", "email": "Correo electrónico", "role": "Rol", "questionnaires": "Cuestionarios", "purchases": "Compras", "no-users-found": "No se encontraron usuarios", "first-name": "Nombre", "last-name": "Apellido", "username": "Nombre de usuario", "phone": "Teléfono", "company": "Empresa", "first-name-required": "El nombre es obligatorio", "last-name-required": "El apellido es obligatorio", "username-required": "El nombre de usuario es obligatorio", "valid-email-required": "Se requiere un correo electrónico válido", "user-added-successfully": "Usuario agregado exitosamente", "error-adding-user": "Error al agregar usuario", "user-updated-successfully": "Usuario actualizado exitosamente", "error-updating-user": "Error al actualizar usuario: {error}", "user-id-not-provided": "ID de usuario no proporcionado", "user-not-found": "Usuario no encontrado", "error-fetching-user": "Error al obtener usuario: {error}", "not-available": "N/A", "invalid-date": "<PERSON><PERSON>", "registration-date": "<PERSON>cha de registro", "last-login": "Último inicio de sesión", "status": "Estado", "active": "Activo", "inactive": "Inactivo", "address": "Dirección", "date-created": "Fecha de creación", "recent-activity": "Actividad reciente", "personal-details": "Detalles personales", "questionnaire-history": "Historial de cuestionarios", "purchase-history": "Historial de compras", "no-questionnaires-found": "No se encontraron cuestionarios", "no-purchases-found": "No se encontraron compras", "role-admin": "Administrador", "role-app-admin": "Administrador de Aplicación", "role-doctor": "Doctor", "role-patient": "Paciente", "role-client": "Cliente", "role-unknown": "Rol desconocido", "title-questionnaire": "Cuestionario", "title-purchase": "Compra", "created-on": "Creado el", "purchased-on": "Comprado el", "no-description-available": "No hay descripción disponible", "quantity": "Cantidad", "total": "Total", "action": "Acción", "save-changes": "Guardar cambios", "discard": "Descar<PERSON>", "save-success": "Cambios guardados exitosamente", "save-error": "Error al guardar cambios", "all-clients": "Todos los clientes", "add-client": "Agregar cliente", "add-new-client": "Agregar nuevo cliente", "add-client-description": "Complete los detalles para agregar un nuevo cliente al sistema.", "no-clients-found": "No se encontraron clientes", "company-name": "Nombre de la empresa", "company-name-required": "El nombre de la empresa es obligatorio", "state": "Estado/Provincia", "city": "Ciudad", "postal-code": "Código postal", "country": "<PERSON><PERSON>", "notes": "Notas", "actions": {"create": "<PERSON><PERSON><PERSON>", "read": "<PERSON><PERSON>", "update": "Actualizar", "delete": "Eliminar", "list": "Listar", "manage": "Gestionar", "view": "<PERSON>er", "edit": "<PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "add": "Agregar", "save": "Guardar", "back": "Atrás", "remove": "Eliminar", "confirm": "Confirmar", "discard": "Descar<PERSON>"}, "confirm-delete": "Confirmar eliminación", "delete-client-confirmation": "¿Está seguro de que desea eliminar {client}? Esta acción no se puede deshacer.", "client-added-successfully": "Cliente agregado exitosamente", "error-adding-client": "Error al agregar cliente", "client-deleted-successfully": "Cliente eliminado exitosamente", "error-deleting-client": "Error al eliminar cliente", "all-clinics": "Todas las clínicas", "add-clinic": "Agregar clínica", "add-new-clinic": "Agregar nueva clínica", "add-clinic-description": "Complete los detalles para agregar una nueva clínica al sistema.", "no-clinics-found": "No se encontraron clínicas", "clinic-name": "Nombre de la clínica", "clinic-name-required": "El nombre de la clínica es obligatorio", "clinic-added-successfully": "Clínica agregada exitosamente", "error-adding-clinic": "Error al agregar clínica", "clinic-deleted-successfully": "Clínica eliminada exitosamente", "error-deleting-clinic": "Error al eliminar clínica", "delete-clinic-confirmation": "¿Está seguro de que desea eliminar {clinic}? Esta acción no se puede deshacer.", "street": "Calle", "zip-code": "Código postal", "back": "Atrás", "error-fetching-clinics": "Error al obtener clínicas: {error}", "clinic-id-not-provided": "ID de clínica no proporcionado", "clinic-not-found": "Clínica no encontrada", "error-fetching-clinic": "Error al obtener clínica: {error}", "clinic-updated-successfully": "Clínica actualizada exitosamente", "error-updating-clinic": "Error al actualizar clínica: {error}", "back-to-clinics": "Volver a clínicas", "clinic-details": "Detalles de la clínica", "page-not-found": "La página que busca no existe.", "page-moved-removed": "La página que busca puede haber sido eliminada, haber cambiado de nombre o estar temporalmente no disponible.", "support": "Soporte", "clients": "Clientes", "patient": "Paciente", "settings": "Configuración", "user-management": "Gestión de usuarios", "clinics": "Clínicas", "patients": "Pacientes", "compliance-reports": "Informes de cumplimiento", "templates": "Plantillas", "trq-system": "Sistema TRQ", "client-management": "Gestión de clientes", "patient-management": "Gestión de pacientes", "clinic-management": "Gestión de clínicas", "system-settings": "Configuración del sistema", "template-management": "Gestión de plantillas", "questionnaire-management": "Gestión de cuestionarios", "compliance-report-management": "Gestión de informes de cumplimiento", "resources": {"users": "Usuarios", "questionnaires": "Cuestionarios", "patients": "Pacientes", "reports": "Informes", "clinic_settings": "Configuración de clínica", "system_settings": "Configuración del sistema", "own_profile": "Perfil propio", "own_questionnaires": "Cuestionarios propios", "own_reports": "Informes propios"}, "enums": {"clearance": {"not_cleared": "No autorizado (0)", "partially_cleared": "Parcialmente autorizado (1)", "mostly_cleared": "Mayormente autorizado (2)", "fully_cleared": "Completamente autorizado (3)"}, "severity": {"low": "Baja", "medium": "Media", "high": "Alta"}, "questionnaire_status": {"created": "<PERSON><PERSON><PERSON>", "in_progress": "En progreso", "completed": "Completado", "reviewed": "<PERSON><PERSON><PERSON>"}, "gender": {"male": "<PERSON><PERSON><PERSON><PERSON>", "female": "Femenino", "other": "<PERSON><PERSON>"}}, "patient-home": "Inicio del paciente", "all-patients": "Todos los pacientes", "back-to-patients": "Volver a pacientes", "no-questionnaires": "No hay cuestionarios disponibles", "all-questionnaires": "Todos los cuestionarios", "search-purchases": "Buscar compras...", "generate-sample-reports": "Generar informes de cumplimiento de muestra", "back-to-questionnaires": "Volver a cuestionarios", "no-purchases": "No hay compras disponibles", "TRQ System": "Sistema TRQ", "Dashboard": "Panel de control", "Patient Home": "Inicio del paciente", "Questionnaires": "Cuestionarios", "Templates": "Plantillas", "Compliance Reports": "Informes de cumplimiento", "Patients": "Pacientes", "Clients": "Clientes", "Clinics": "Clínicas", "User Management": "Gestión de usuarios", "All Users": "Todos los usuarios", "Purchases": "Compras", "All Purchases": "Todas las compras", "Settings": "Configuración", "completed-at": "Completado el", "select-template": "Seleccionar <PERSON>", "start-questionnaire": "Iniciar <PERSON>", "search": "Buscar", "all": "Todos", "in-progress": "En Progreso", "completed": "Completado", "reviewed": "<PERSON><PERSON><PERSON>", "assigned": "<PERSON><PERSON><PERSON>", "report-id": "ID del Informe", "doctor": "Doctor", "created-date": "Fecha de Creación", "follow-up": "Segu<PERSON><PERSON><PERSON>", "filters": "<PERSON><PERSON><PERSON>", "filter-by": "Filtrar por", "clear-filters": "Limpiar <PERSON>", "apply-filters": "Aplicar Filtros", "filter-results": "Resultados Filtrados", "no-filters-applied": "No hay filtros aplicados", "filter-status": "Filtrar por Estado", "filter-date": "Filtrar por Fecha", "filter-doctor": "Filtrar por Doctor", "filter-patient": "Filtrar por Paciente", "filter-client": "Filtrar por Cliente", "filter-clearance": "Filtrar por Nivel de Autorización", "filter-severity": "Filtrar por Nivel de Severidad", "showing-reports": "Mostrando {count} de {total} Informes", "date-of-birth": "<PERSON><PERSON> de Nacimiento", "delete-patient-confirmation": "¿Está seguro de que desea eliminar a {patient}? Esta acción no se puede deshacer.", "patient-list-coming-soon": "La funcionalidad de lista de pacientes estará disponible próximamente", "medical-evaluation": "Evaluación médica", "respirator-clearance": "Autorización del respirador", "clearance-description": "Descripción de la autorización", "clearance-based-on": "Autorización basada en", "follow-up-required": "Seguimiento requerido", "yes": "Sí", "no": "No", "follow-up-description": "Descripción del seguimiento", "workload-limitation": "Limitación de carga de trabajo (%)", "medical-findings": "Hallazgos Médicos", "category": "Categoría", "description": "Descripción", "severity": "Severidad", "remove": "Eliminar", "add-medical-finding": "Agregar hallazgo médico", "report-not-found": "Informe no encontrado", "error-fetching-report": "Error al obtener el informe", "respirator-clearance-required": "Se requiere autorización del respirador", "clearance-based-on-required": "Se requiere la base de la autorización", "category-required": "La categoría es requerida", "description-required": "La descripción es requerida", "severity-required": "La severidad es requerida", "error-updating-report": "Error al actualizar el informe", "edit-compliance-report": "Editar informe de cumplimiento", "review-compliance-report": "Revisar informe de cumplimiento", "report-details": "Detalles del informe", "patient-id": "ID del paciente", "doctor-id": "ID del doctor", "questionnaire-id": "ID del cuestionario", "medical-assessment": "Evaluación médica", "respirator-clearance-description": "Descripción de la Autorización del Respirador", "add-finding": "Agregar hallazgo", "no-medical-findings": "No se registraron hallazgos médicos", "save-draft": "Guardar borrador", "deny": "<PERSON><PERSON><PERSON>", "approve-with-suggestions": "Aprobar con sugerencias", "approve": "<PERSON><PERSON><PERSON>", "add-patient": "<PERSON><PERSON><PERSON><PERSON>", "client-id": "ID del Cliente", "add-new-patient": "Agregar Nuevo Paciente", "add-patient-description": "Complete los detalles para agregar un nuevo paciente al sistema.", "patient-added-successfully": "Paciente agregado exitosamente", "error-adding-patient": "Error al agregar paciente", "patient-updated-successfully": "Paciente actualizado exitosamente", "error-updating-patient": "Error al actualizar paciente: {error}", "patient-id-not-provided": "ID de paciente no proporcionado", "error-fetching-patient": "Error al obtener paciente: {error}", "client-name": "Nombre del Cliente", "total-quantity": "Cantidad Total", "total-price": "Precio Total", "payment-status": "Estado del Pago", "purchase-date": "<PERSON><PERSON>", "create-purchase": "<PERSON><PERSON><PERSON>", "all-statuses": "Todos los Estados", "compliance-report-details": "Detalles del Informe de Cumplimiento", "review": "Rev<PERSON><PERSON>", "view-pdf": "Ver PDF", "signed-date": "<PERSON><PERSON>", "questionnaire": "Cuestionario", "back-to-reports": "Volver a Informes", "unknown": "Desconocido", "filter-options": {"all": "Todos", "pending": "Pendiente", "approved": "Aprobado", "denied": "Deneg<PERSON>", "draft": "<PERSON><PERSON><PERSON>", "last-week": "Última Semana", "last-month": "<PERSON><PERSON><PERSON>", "last-quarter": "<PERSON><PERSON><PERSON>", "custom-range": "<PERSON><PERSON>"}, "assigned-to": "Asignado a", "assigned-doctor": "Doctor as<PERSON><PERSON>", "no-address-available": "No hay información de dirección disponible", "add-comment": "Agregar comentario", "enter-your-comment": "Ingrese su comentario aquí...", "questionnaire-not-found": "Cuestionario no encontrado", "error": {"unauthorized": {"title": "401", "heading": "Acceso no autorizado", "message": "No tienes permiso para acceder a {resource}. Por favor, contacta a tu administrador si crees que esto es un error."}, "fetch": {"doctors": "Error al obtener la lista de doctores."}}, "delete-user-confirmation": "¿Está seguro de que desea eliminar {count, plural, one {el usuario seleccionado} other {# usuarios seleccionados}}? Esta acción no se puede deshacer.", "create-from-template": "<PERSON><PERSON><PERSON> desde plantilla", "create-questionnaire": "<PERSON><PERSON><PERSON> cuestion<PERSON>", "assign": "<PERSON><PERSON><PERSON>", "assign-questionnaire": "<PERSON><PERSON><PERSON> cues<PERSON>", "assigned-on": "Asignado el", "user-deleted-successfully": "Se ha eliminado {count, plural, one {usuario} other {# usuarios}} exitosamente.", "error-deleting-user": "Error al eliminar usuario(s).", "operator": "Operador", "respirator-clearance-error": "La autorización del respirador debe ser seleccionada", "workload-limitation-error": "La limitación de carga de trabajo debe estar entre 0 y 100", "follow-up-description-required": "La descripción del seguimiento es requerida cuando el seguimiento está habilitado", "sample-reports-description": "Esta utilidad generará informes de cumplimiento de muestra para fines de prueba. Creará informes con diferentes estados (borrador, revisión pendiente, completado) utilizando pacientes, médicos y cuestionarios existentes en la base de datos.", "generating": "Generando...", "generate-reports": "Generar Informes de Muestra", "error-generating-reports": "Error al generar informes", "reports-generated-success": "Se generaron con éxito {count} informes de muestra", "generated-reports": "Informes Generados", "report": "Informe", "client-id-missing": "Falta el ID del cliente", "error-fetching-client-data": "Error al obtener datos del cliente", "client-info": "Información del Cliente", "contact-name": "Nombre de Contacto", "associated-patients": "Pacientes Asociados", "no-associated-patients": "No se encontraron pacientes para este cliente", "purchase-list-coming-soon": "Historial de compras próximamente", "firebase-uid": "UID de Firebase", "clear": "Limpiar", "compliance-reports-title": "Informes de Cumplimiento", "draft": "<PERSON><PERSON><PERSON>", "finalized": "Finalizado", "archived": "Archivado", "search-placeholder": "Buscar por ID, paciente o doctor", "contact-email": "Correo Electrónico de Contacto", "contact-phone": "Teléfono de Contacto", "city-state": "Ciudad, Estado", "edit-clinic": "<PERSON><PERSON>", "administrators": "Administradores", "no-administrators": "No hay administradores asignados a esta clínica", "none": "<PERSON><PERSON><PERSON>", "edit-patient": "<PERSON><PERSON>", "roles": {"admin_description": "Administrador del sistema completo con todos los permisos", "clinic_admin_description": "Administrador de clínica con permisos a nivel de clínica", "client_description": "Cliente con acceso limitado a datos", "doctor_description": "Médico con permisos clínicos", "patient_description": "Paciente con acceso solo a sus propios datos"}, "clinic-info": "Información de la Clínica", "admin-not-found": "Administrador no encontrado", "error-fetching-admin": "Error al obtener administrador: {error}", "admin-updated-successfully": "Administrador actualizado exitosamente", "clinic-admin-details": "Detalles del administrador de clínica", "no-data-found": "No se encontraron datos", "loading-data": "Cargando datos...", "required-field": "Este campo es requerido", "invalid-email": "Dirección de correo electrónico inválida", "success-message": "Operación completada exitosamente", "validation-error": "Error de validación"}