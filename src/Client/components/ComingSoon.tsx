import React from 'react';
import { Box, Typography, Button } from '@mui/material';
import { useTheme } from '@mui/material/styles';
import MainCard from '[components]/cards/MainCard';
import { gridSpacing } from '[constants]/gridSpacing';
import ComingSoonIllustration from 'assets/images/coming-soon.svg';

interface ComingSoonProps {
  title?: string;
  description?: string;
  actionText?: string;
  onAction?: () => void;
}

const ComingSoon = ({
  title = 'Coming Soon',
  description = 'This feature is under development and will be available soon.',
  actionText = 'Go Back',
  onAction
}: ComingSoonProps) => {
  const theme = useTheme();

  return (
    <MainCard>
      <Box sx={{ padding: 3, textAlign: 'center' }}>
        <Box sx={{ mb: 3 }}>
          <img src={ComingSoonIllustration} alt="Coming Soon" style={{ maxWidth: '100%', height: 'auto', maxHeight: '300px' }} />
        </Box>
        <Typography variant="h2" gutterBottom>
          {title}
        </Typography>
        <Typography variant="body1" sx={{ mb: 3 }}>
          {description}
        </Typography>
        {onAction && (
          <Button variant="contained" color="primary" onClick={onAction}>
            {actionText}
          </Button>
        )}
      </Box>
    </MainCard>
  );
};

export default ComingSoon;
