import React from 'react';
import { lazy } from 'react';
import Loadable from '[components]/Loadable';
import PermissionRoute from 'RBAC/PermissionRoute';
import { PERMISSIONS } from 'RBAC/permissions';
import ROUTES from 'Routing/appRoutes';

// Clients
const AllClients = Loadable(lazy(() => import('./[pages]/AllClients')));
const AddClient = Loadable(lazy(() => import('./[pages]/AddClient')));
const ClientDashboard = Loadable(lazy(() => import('./[pages]/ClientDashboard')));
const ClientDetails = Loadable(lazy(() => import('./[pages]/ClientDetails')));
const EditClient = Loadable(lazy(() => import('./[pages]/EditClient')));
const ClientProfile = Loadable(lazy(() => import('./[pages]/ClientProfile')));
const MyPatients = Loadable(lazy(() => import('./[pages]/MyPatients')));

const clientRoutes = {
  path: 'clients', // Updated base path from 'Client' to 'clients'
  children: [
    { path: '', element: <AllClients /> },
    { path: ROUTES.CLIENTS.ADD_PATTERN, element: <AddClient /> },
    // TODO: Add PermissionRoute wrappers to existing client routes for consistency
    { path: 'home', element: <ClientDashboard /> }, // Updated path from 'Home' to 'home' to match convention
    { path: ROUTES.CLIENTS.DETAILS_PATTERN, element: <ClientDetails /> },
    { path: ROUTES.CLIENTS.EDIT_PATTERN, element: <EditClient /> },
    {
      path: ROUTES.CLIENTS.PROFILE_PATTERN,
      element: (
        <PermissionRoute resource={PERMISSIONS.VIEW_OWN_PROFILE.resource} permissionAction={PERMISSIONS.VIEW_OWN_PROFILE.action}>
          <ClientProfile />
        </PermissionRoute>
      )
    },
    {
      path: 'my-patients',
      element: (
        <PermissionRoute resource={PERMISSIONS.LIST_PATIENTS.resource} permissionAction={PERMISSIONS.LIST_PATIENTS.action}>
          <MyPatients />
        </PermissionRoute>
      )
    }
  ]
};

export default clientRoutes;
