import React, { createContext, useContext, useEffect, useState } from 'react';
import { User, UserCredential } from 'firebase/auth';
import { auth } from '../../Firebase/[config]/firebase';
import { TRQUser } from '../../Users/<USER>/User';
import { useAuthState, useSignInWithEmailAndPassword, useSignInWithGoogle } from 'react-firebase-hooks/auth';
import { getDashboardUrlForRole } from '../../Routing/appRoutes';
import { Role } from '../../RBAC/[types]/Role';

// Define the context type
interface AuthContextType {
  auth: typeof auth;
  currentUser: User | null;
  userData: TRQUser | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  signInWithEmailAndPassword: (email: string, password: string) => Promise<UserCredential | undefined>;
  signInWithGoogle: () => Promise<UserCredential | undefined>;
  signUp: (email: string, password: string, firstName?: string, lastName?: string) => Promise<void>;
  signOut: () => Promise<void>;
  logout: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  updateProfile: (displayName?: string, photoURL?: string) => Promise<void>;
  changePassword: (currentPassword: string, newPassword: string) => Promise<void>;
  error: Error | null;
  loginError: Error | null;
  loginUser: UserCredential | undefined;
  loginLoading: boolean;
  googleError: Error | null;
  googleUser: UserCredential | undefined;
  googleLoading: boolean;
  homeUrl: string | undefined;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Auth state from Firebase
  const [firebaseUser, loading, authError] = useAuthState(auth);
  // Custom user data
  const [userData, setUserData] = useState<TRQUser | null>(null);
  const [error, setError] = useState<Error | null>(null);

  // Login hooks from react-firebase-hooks/auth
  const [signInWithEmailAndPassword, loginUser, loginLoading, rawLoginError] = useSignInWithEmailAndPassword(auth);
  const [signInWithGoogle, googleUser, googleLoading, rawGoogleError] = useSignInWithGoogle(auth);

  useEffect(() => {
    if (!firebaseUser) {
      setUserData(null);
      return;
    }
    const fetchUserData = async () => {
      try {
        const { ensureUserExists } = await import('../../Users/<USER>/userService');
        const userDoc = await ensureUserExists(firebaseUser);
        setUserData(userDoc);
      } catch (err) {
        // Check if this is a Firebase permissions error
        const isPermissionsError =
          err instanceof Error &&
          (err.message.includes('permission') ||
            err.message.includes('Permission') ||
            err.message.includes('Missing or insufficient permissions'));

        if (isPermissionsError) {
          // Set a specific error to notify developers about Firestore security rules
          const securityError = new Error(
            'FIRESTORE SECURITY RULES ERROR: Missing or insufficient permissions when trying to access user data. ' +
              'Please update your Firestore security rules to allow authenticated users to read/write their own user documents. ' +
              'In Firebase Console, go to Firestore > Rules and add rules that allow access based on authentication.'
          );
          console.error(securityError);
          setError(securityError);

          // Still provide fallback user data so the app doesn't crash completely
          const defaultUserData: TRQUser = {
            uid: firebaseUser.uid,
            email: firebaseUser.email || '',
            firstName: firebaseUser.displayName?.split(' ')[0] || '',
            lastName: firebaseUser.displayName?.split(' ').slice(1).join(' ') || '',
            role: Role.Patient, // Default role
            isActive: true
          };
          setUserData(defaultUserData);
        } else {
          // Handle other errors
          console.error('Error fetching user data:', err);
          setError(err instanceof Error ? err : new Error('Failed to fetch user data'));
        }
      }
    };
    fetchUserData();
  }, [firebaseUser]);

  // Compute homeUrl using shared utility
  const homeUrl = userData ? getDashboardUrlForRole(userData.role) : undefined;

  // Debug homeUrl computation
  useEffect(() => {
    if (userData) {
      console.log('=== AUTH CONTEXT homeUrl DEBUG ===');
      console.log('userData:', userData);
      console.log('userData.role:', userData.role);
      console.log('typeof userData.role:', typeof userData.role);
      console.log('Role enum values:', Object.values(Role));
      console.log('computed homeUrl:', homeUrl);
      console.log('getDashboardUrlForRole direct call:', getDashboardUrlForRole(userData.role));
    }
  }, [userData, homeUrl]);

  // Auth actions (custom abstractions only for things not handled by hooks)
  const signUp = async (email: string, password: string, firstName?: string, lastName?: string) => {
    /* implement as needed */
  };
  const signOut = async () => auth.signOut();
  const logout = signOut;
  const resetPassword = async (email: string) => {
    /* implement as needed */
  };
  const updateProfile = async (displayName?: string, photoURL?: string) => {
    /* implement as needed */
  };
  const changePassword = async (currentPassword: string, newPassword: string) => {
    /* implement as needed */
  };

  const value = {
    auth,
    currentUser: firebaseUser ?? null,
    userData,
    isAuthenticated: !!firebaseUser,
    isLoading: loading,
    // Expose the raw Firebase hook functions
    signInWithEmailAndPassword,
    signInWithGoogle,
    signUp,
    signOut,
    logout,
    resetPassword,
    updateProfile,
    changePassword,
    error: error || authError || null,
    loginLoading,
    loginError: rawLoginError || null,
    loginUser,
    googleLoading,
    googleError: rawGoogleError || null,
    googleUser,
    homeUrl
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

// Custom hook to use the auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthContext;
