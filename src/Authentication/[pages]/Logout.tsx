import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from 'Authentication/[contexts]/AuthContext';
import { Box, CircularProgress, Typography, Fade } from '@mui/material';
import ROUTES from 'Routing/appRoutes';

const Logout = () => {
  const { logout } = useAuth();
  const navigate = useNavigate();
  const [message, setMessage] = useState('Logging out...');

  useEffect(() => {
    const performLogout = async () => {
      try {
        console.log('Starting logout process...');
        setMessage('Logging out...');

        // Perform the logout
        await logout();
        console.log('Logout successful, redirecting to login...');

        // Show success message briefly
        setMessage('Logged out successfully!');

        // Wait a moment before redirecting
        setTimeout(() => {
          navigate(ROUTES.AUTH.LOGIN, { replace: true });
        }, 1500);
      } catch (error) {
        console.error('Logout error:', error);
        setMessage('Logout failed. Redirecting...');

        // Still redirect even if logout fails
        setTimeout(() => {
          navigate(ROUTES.AUTH.LOGIN, { replace: true });
        }, 2000);
      }
    };

    performLogout();
  }, [logout, navigate]);

  return (
    <Box display="flex" flexDirection="column" alignItems="center" justifyContent="center" minHeight="80vh" gap={2}>
      <Fade in={true} timeout={500}>
        <CircularProgress size={60} />
      </Fade>
      <Fade in={true} timeout={800}>
        <Typography variant="h6" color="textSecondary" textAlign="center">
          {message}
        </Typography>
      </Fade>
    </Box>
  );
};

export default Logout;
