import { SyntheticEvent, useState, useEffect } from 'react';
import { useNavigate, Link as RouterLink } from 'react-router-dom';
import { useTheme } from '@mui/material/styles';
import {
  Box,
  Button,
  Checkbox,
  Divider,
  FormControl,
  FormControlLabel,
  FormHelperText,
  Grid,
  IconButton,
  InputAdornment,
  InputLabel,
  OutlinedInput,
  Stack,
  Typography,
  useMediaQuery,
  Paper,
  Container,
  Alert,
  Snackbar,
  CircularProgress
} from '@mui/material';

// third party
import * as Yup from 'yup';
import { Formik } from 'formik';

// project imports
import AnimateButton from '[components]/extended/AnimateButton';

// assets
import Visibility from '@mui/icons-material/Visibility';
import VisibilityOff from '@mui/icons-material/VisibilityOff';
import Google from '@mui/icons-material/Google';
import { useAuth } from 'Authentication/[contexts]/AuthContext';
import { getDashboardUrlForRole } from 'Routing/appRoutes';
import { Role } from 'RBAC/[types]/Role';

// ============================|| TRQ - LOGIN ||============================ //

const TRQLogin = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const matchDownSM = useMediaQuery(theme.breakpoints.down('md'));
  const [checked, setChecked] = useState(true);
  const [showPassword, setShowPassword] = useState(false);
  const [notification, setNotification] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'warning' | 'info';
  }>({ open: false, message: '', severity: 'info' });

  // Auth context
  const {
    signInWithEmailAndPassword,
    signInWithGoogle,
    googleLoading,
    loginError,
    googleError,
    currentUser,
    homeUrl,
    userData,
    isLoading
  } = useAuth();

  // Show notifications on error
  useEffect(() => {
    if (loginError) {
      setNotification({ open: true, message: loginError.message, severity: 'error' });
    } else if (googleError) {
      setNotification({ open: true, message: googleError.message, severity: 'error' });
    }
  }, [loginError, googleError]);

  // Navigate to root after successful login
  useEffect(() => {
    if (currentUser) {
      console.log('=== LOGIN NAVIGATION DEBUG ===');
      console.log('currentUser:', currentUser);
      console.log('userData:', userData);
      console.log('userData.role:', userData?.role);
      console.log('homeUrl:', homeUrl);
      console.log('isLoading:', isLoading);
      console.log('getDashboardUrlForRole result:', userData ? getDashboardUrlForRole(userData.role) : 'no userData');

      if (homeUrl) {
        console.log('✅ Navigating to computed homeUrl:', homeUrl);
        navigate(homeUrl, { replace: true });
      } else if (userData) {
        // If we have userData but no homeUrl, something is wrong with getDashboardUrlForRole
        console.warn('⚠️ User data available but no homeUrl computed:', userData);
        console.log('User role type:', typeof userData.role);
        console.log('User role value:', userData.role);
        console.log('Available Role enum values:', Object.values(Role));

        // Try calling getDashboardUrlForRole directly to debug
        const directResult = getDashboardUrlForRole(userData.role);
        console.log('Direct getDashboardUrlForRole call result:', directResult);

        // Fallback to a default route based on role
        const fallbackUrl = `/trq/${userData.role.toLowerCase()}s/home`;
        console.log('Using fallback URL:', fallbackUrl);
        navigate(fallbackUrl, { replace: true });
      } else if (!isLoading) {
        // If not loading but no userData, there might be an error
        console.warn('⚠️ User authenticated but no userData and not loading. This might indicate a Firestore issue.');
      }
    }
  }, [currentUser, navigate, homeUrl, userData, isLoading]);

  const handleClickShowPassword = () => {
    setShowPassword(!showPassword);
  };

  const handleMouseDownPassword = (event: SyntheticEvent) => {
    event.preventDefault();
  };

  const handleLogin = async (values: { email: string; password: string }, { setErrors, setStatus, setSubmitting }: any) => {
    try {
      await signInWithEmailAndPassword(values.email, values.password);
      setStatus({ success: true });
      setSubmitting(false);
    } catch (err: any) {
      setErrors({ submit: err.message });
      setStatus({ success: false });
      setSubmitting(false);
    }
  };

  const googleHandler = async () => {
    try {
      await signInWithGoogle();
    } catch (err: any) {
      setNotification({ open: true, message: err.message, severity: 'error' });
    }
  };

  const handleCloseNotification = () => {
    setNotification((prev) => ({ ...prev, open: false }));
  };

  return (
    <Container maxWidth="sm">
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '100vh' }}>
        <Paper elevation={10} sx={{ p: 4, width: '100%' }}>
          <Grid container direction="column" justifyContent="center" spacing={2}>
            <Grid item xs={12}>
              <Box
                sx={{
                  alignItems: 'center',
                  display: 'flex'
                }}
              >
                <Divider sx={{ flexGrow: 1 }} orientation="horizontal" />
                <Typography variant="h4" sx={{ m: 2 }}>
                  Titanium Respiratory Questionnaires
                </Typography>
                <Divider sx={{ flexGrow: 1 }} orientation="horizontal" />
              </Box>
            </Grid>
            <Grid item xs={12}>
              <Grid container direction={matchDownSM ? 'column-reverse' : 'row'} alignItems="center" justifyContent="center">
                <Grid item>
                  <Stack alignItems="center" justifyContent="center" spacing={1}>
                    <Typography color={theme.palette.secondary.main} gutterBottom variant={matchDownSM ? 'h3' : 'h2'}>
                      Sign In
                    </Typography>
                    <Typography variant="caption" fontSize="16px" textAlign={matchDownSM ? 'center' : 'inherit'}>
                      Enter your credentials to continue
                    </Typography>
                  </Stack>
                </Grid>
              </Grid>
            </Grid>
            <Grid item xs={12}>
              <AnimateButton>
                <Button
                  disableElevation
                  fullWidth
                  onClick={googleHandler}
                  size="large"
                  variant="outlined"
                  sx={{
                    color: 'grey.700',
                    backgroundColor: theme.palette.mode === 'dark' ? theme.palette.dark.main : theme.palette.grey[50],
                    borderColor: theme.palette.mode === 'dark' ? theme.palette.dark.light + 20 : theme.palette.grey[100]
                  }}
                  data-testid="login-google-button"
                  disabled={googleLoading}
                >
                  <Box sx={{ mr: { xs: 1, sm: 2 }, display: 'flex', justifyContent: 'center' }}>
                    <Google fontSize="small" color="primary" />
                    {googleLoading && <CircularProgress size={18} sx={{ ml: 1 }} />}
                  </Box>
                  Sign in with Google
                </Button>
              </AnimateButton>
            </Grid>
            <Grid item xs={12}>
              <Box
                sx={{
                  alignItems: 'center',
                  display: 'flex'
                }}
              >
                <Divider sx={{ flexGrow: 1 }} orientation="horizontal" />

                <Button
                  variant="outlined"
                  sx={{
                    cursor: 'unset',
                    m: 2,
                    py: 0.5,
                    px: 7,
                    borderColor:
                      theme.palette.mode === 'dark'
                        ? `${theme.palette.dark.light + 20} !important`
                        : `${theme.palette.grey[100]} !important`,
                    color: theme.palette.grey[900],
                    fontWeight: 500,
                    borderRadius: `${theme.shape.borderRadius}px`
                  }}
                  disableRipple
                  disabled
                >
                  OR
                </Button>

                <Divider sx={{ flexGrow: 1 }} orientation="horizontal" />
              </Box>
            </Grid>
            <Grid item xs={12} container alignItems="center" justifyContent="center">
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle1">Sign in with Email</Typography>
              </Box>
            </Grid>
          </Grid>

          <Formik
            initialValues={{
              email: '',
              password: '',
              submit: null
            }}
            validationSchema={Yup.object().shape({
              email: Yup.string().email('Must be a valid email').max(255).required('Email is required'),
              password: Yup.string().max(255).required('Password is required')
            })}
            onSubmit={handleLogin}
          >
            {({ errors, handleBlur, handleChange, handleSubmit, isSubmitting, touched, values }) => (
              <form noValidate onSubmit={handleSubmit}>
                <FormControl fullWidth error={Boolean(touched.email && errors.email)} sx={{ ...theme.typography.customInput }}>
                  <InputLabel htmlFor="outlined-adornment-email-login">Email Address</InputLabel>
                  <OutlinedInput
                    id="outlined-adornment-email-login"
                    type="email"
                    value={values.email}
                    name="email"
                    onBlur={handleBlur}
                    onChange={handleChange}
                    label="Email Address"
                    inputProps={{
                      'data-testid': 'login-email-input'
                    }}
                  />
                  {touched.email && errors.email && (
                    <FormHelperText error id="standard-weight-helper-text-email-login" data-testid="login-error-message">
                      {errors.email}
                    </FormHelperText>
                  )}
                </FormControl>

                <FormControl fullWidth error={Boolean(touched.password && errors.password)} sx={{ ...theme.typography.customInput }}>
                  <InputLabel htmlFor="outlined-adornment-password-login">Password</InputLabel>
                  <OutlinedInput
                    id="outlined-adornment-password-login"
                    type={showPassword ? 'text' : 'password'}
                    value={values.password}
                    name="password"
                    onBlur={handleBlur}
                    onChange={handleChange}
                    endAdornment={
                      <InputAdornment position="end">
                        <IconButton
                          aria-label="toggle password visibility"
                          onClick={handleClickShowPassword}
                          onMouseDown={handleMouseDownPassword}
                          edge="end"
                          size="large"
                        >
                          {showPassword ? <Visibility /> : <VisibilityOff />}
                        </IconButton>
                      </InputAdornment>
                    }
                    label="Password"
                    inputProps={{
                      'data-testid': 'login-password-input'
                    }}
                  />
                  {touched.password && errors.password && (
                    <FormHelperText error id="standard-weight-helper-text-password-login" data-testid="login-error-message">
                      {errors.password}
                    </FormHelperText>
                  )}
                </FormControl>
                <Stack direction="row" alignItems="center" justifyContent="space-between" spacing={1}>
                  <FormControlLabel
                    control={
                      <Checkbox checked={checked} onChange={(event) => setChecked(event.target.checked)} name="checked" color="primary" />
                    }
                    label="Remember me"
                  />
                  <Typography variant="subtitle1" color="secondary" sx={{ textDecoration: 'none', cursor: 'pointer' }}>
                    Forgot Password?
                  </Typography>
                </Stack>
                {errors.submit && (
                  <Box sx={{ mt: 3 }}>
                    <FormHelperText error data-testid="login-error-message">
                      {errors.submit}
                    </FormHelperText>
                  </Box>
                )}

                <Box sx={{ mt: 2 }}>
                  <AnimateButton>
                    <Button
                      disableElevation
                      fullWidth
                      size="large"
                      type="submit"
                      variant="contained"
                      color="secondary"
                      data-testid="login-submit-button"
                    >
                      Sign in
                    </Button>
                  </AnimateButton>
                </Box>
                <Grid container justifyContent="center" alignItems="center" sx={{ mt: 3 }}>
                  <Typography variant="subtitle1">
                    Don't have an account?{' '}
                    <RouterLink to="/trq/register" style={{ textDecoration: 'none' }} data-testid="login-signup-link">
                      <Typography component="span" variant="subtitle1" sx={{ color: theme.palette.secondary.main, cursor: 'pointer' }}>
                        Sign Up
                      </Typography>
                    </RouterLink>
                  </Typography>
                </Grid>
              </form>
            )}
          </Formik>
        </Paper>
      </Box>

      {/* Notification snackbar */}
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={handleCloseNotification}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert
          onClose={handleCloseNotification}
          severity={notification.severity}
          sx={{ width: '100%' }}
          data-testid="login-notification-alert"
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default TRQLogin;
