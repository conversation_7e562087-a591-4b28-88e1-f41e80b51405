/**
 * Linear Project List Component
 *
 * Displays a list of Linear projects with filtering and actions
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  CardActions,
  Typography,
  Chip,
  Button,
  Grid,
  TextField,
  InputAdornment,
  IconButton,
  Menu,
  MenuItem,
  LinearProgress,
  Avatar,
  AvatarGroup,
  Tooltip,
  Stack,
  Alert
} from '@mui/material';
import {
  Search as SearchIcon,
  FilterList as FilterIcon,
  Add as AddIcon,
  MoreVert as MoreVertIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  Group as TeamIcon,
  CalendarToday as CalendarIcon,
  TrendingUp as ProgressIcon
} from '@mui/icons-material';

import { useLinear } from '../[contexts]/LinearContext';
import { linearService } from '../[services]/linearService';
import { LinearProject, ProjectState, ProjectFilter } from '../[types]/Linear';

interface ProjectListProps {
  onProjectSelect?: (project: LinearProject) => void;
  onProjectCreate?: () => void;
  onProjectEdit?: (project: LinearProject) => void;
  onProjectDelete?: (project: LinearProject) => void;
}

export default function ProjectList({ onProjectSelect, onProjectCreate, onProjectEdit, onProjectDelete }: ProjectListProps) {
  const { isAuthenticated, selectedTeamId, teams } = useLinear();
  const [projects, setProjects] = useState<LinearProject[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [filter, setFilter] = useState<ProjectFilter>({});
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedProject, setSelectedProject] = useState<LinearProject | null>(null);

  // Load projects
  useEffect(() => {
    if (isAuthenticated) {
      loadProjects();
    }
  }, [isAuthenticated, selectedTeamId, filter]);

  const loadProjects = async () => {
    setLoading(true);
    setError(null);
    try {
      const projectFilter: ProjectFilter = {
        ...filter,
        ...(selectedTeamId && { teamIds: [selectedTeamId] }),
        ...(searchQuery && { search: searchQuery })
      };

      const projectList = await linearService.getProjects(projectFilter);
      setProjects(projectList);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load projects');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, project: LinearProject) => {
    setAnchorEl(event.currentTarget);
    setSelectedProject(project);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedProject(null);
  };

  const handleProjectAction = (action: 'view' | 'edit' | 'delete') => {
    if (!selectedProject) return;

    switch (action) {
      case 'view':
        onProjectSelect?.(selectedProject);
        break;
      case 'edit':
        onProjectEdit?.(selectedProject);
        break;
      case 'delete':
        onProjectDelete?.(selectedProject);
        break;
    }
    handleMenuClose();
  };

  const getStateColor = (state: ProjectState): string => {
    switch (state) {
      case ProjectState.BACKLOG:
        return 'default';
      case ProjectState.PLANNED:
        return 'info';
      case ProjectState.STARTED:
        return 'primary';
      case ProjectState.PAUSED:
        return 'warning';
      case ProjectState.COMPLETED:
        return 'success';
      case ProjectState.CANCELED:
        return 'error';
      default:
        return 'default';
    }
  };

  const formatDate = (date?: Date): string => {
    if (!date) return 'Not set';
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    }).format(date);
  };

  if (!isAuthenticated) {
    return <Alert severity="warning">Please authenticate with Linear to view projects.</Alert>;
  }

  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1">
          Linear Projects
        </Typography>
        <Button variant="contained" startIcon={<AddIcon />} onClick={onProjectCreate} disabled={!selectedTeamId}>
          New Project
        </Button>
      </Box>

      {/* Search and Filters */}
      <Box mb={3}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              placeholder="Search projects..."
              value={searchQuery}
              onChange={handleSearch}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                )
              }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <Box display="flex" gap={1}>
              <IconButton>
                <FilterIcon />
              </IconButton>
            </Box>
          </Grid>
        </Grid>
      </Box>

      {/* Loading */}
      {loading && <LinearProgress sx={{ mb: 2 }} />}

      {/* Error */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* Projects Grid */}
      <Grid container spacing={3}>
        {projects.map((project) => (
          <Grid item xs={12} md={6} lg={4} key={project.id}>
            <Card
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                cursor: 'pointer',
                '&:hover': {
                  boxShadow: 4
                }
              }}
              onClick={() => onProjectSelect?.(project)}
            >
              <CardContent sx={{ flexGrow: 1 }}>
                {/* Project Header */}
                <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
                  <Box flexGrow={1}>
                    <Typography variant="h6" component="h3" noWrap>
                      {project.name}
                    </Typography>
                    <Chip label={project.state} color={getStateColor(project.state) as any} size="small" sx={{ mt: 1 }} />
                  </Box>
                  <IconButton
                    size="small"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleMenuOpen(e, project);
                    }}
                  >
                    <MoreVertIcon />
                  </IconButton>
                </Box>

                {/* Description */}
                {project.description && (
                  <Typography
                    variant="body2"
                    color="text.secondary"
                    sx={{
                      mb: 2,
                      display: '-webkit-box',
                      WebkitLineClamp: 3,
                      WebkitBoxOrient: 'vertical',
                      overflow: 'hidden'
                    }}
                  >
                    {project.description}
                  </Typography>
                )}

                {/* Progress */}
                <Box mb={2}>
                  <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                    <Typography variant="body2" color="text.secondary">
                      Progress
                    </Typography>
                    <Typography variant="body2" fontWeight="medium">
                      {Math.round(project.progress * 100)}%
                    </Typography>
                  </Box>
                  <LinearProgress variant="determinate" value={project.progress * 100} sx={{ height: 6, borderRadius: 3 }} />
                </Box>

                {/* Stats */}
                <Stack direction="row" spacing={2} mb={2}>
                  <Box display="flex" alignItems="center" gap={0.5}>
                    <ProgressIcon fontSize="small" color="action" />
                    <Typography variant="body2" color="text.secondary">
                      {project.completedIssueCount}/{project.issueCount} issues
                    </Typography>
                  </Box>
                  <Box display="flex" alignItems="center" gap={0.5}>
                    <TeamIcon fontSize="small" color="action" />
                    <Typography variant="body2" color="text.secondary">
                      {project.teams.length} teams
                    </Typography>
                  </Box>
                </Stack>

                {/* Team Members */}
                {project.members.length > 0 && (
                  <Box mb={2}>
                    <Typography variant="body2" color="text.secondary" mb={1}>
                      Team Members
                    </Typography>
                    <AvatarGroup max={4} sx={{ justifyContent: 'flex-start' }}>
                      {project.members.map((member) => (
                        <Tooltip key={member.id} title={member.displayName}>
                          <Avatar src={member.avatarUrl} alt={member.displayName} sx={{ width: 32, height: 32 }}>
                            {member.displayName.charAt(0)}
                          </Avatar>
                        </Tooltip>
                      ))}
                    </AvatarGroup>
                  </Box>
                )}

                {/* Dates */}
                <Box>
                  <Stack direction="row" spacing={2}>
                    <Box>
                      <Typography variant="caption" color="text.secondary">
                        Start Date
                      </Typography>
                      <Typography variant="body2">{formatDate(project.startDate)}</Typography>
                    </Box>
                    <Box>
                      <Typography variant="caption" color="text.secondary">
                        Target Date
                      </Typography>
                      <Typography variant="body2">{formatDate(project.targetDate)}</Typography>
                    </Box>
                  </Stack>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Empty State */}
      {!loading && projects.length === 0 && (
        <Box textAlign="center" py={8}>
          <Typography variant="h6" color="text.secondary" mb={2}>
            No projects found
          </Typography>
          <Typography variant="body2" color="text.secondary" mb={3}>
            {searchQuery ? 'Try adjusting your search criteria' : 'Create your first project to get started'}
          </Typography>
          {!searchQuery && (
            <Button variant="contained" startIcon={<AddIcon />} onClick={onProjectCreate} disabled={!selectedTeamId}>
              Create Project
            </Button>
          )}
        </Box>
      )}

      {/* Context Menu */}
      <Menu anchorEl={anchorEl} open={Boolean(anchorEl)} onClose={handleMenuClose}>
        <MenuItem onClick={() => handleProjectAction('view')}>
          <ViewIcon sx={{ mr: 1 }} />
          View Details
        </MenuItem>
        <MenuItem onClick={() => handleProjectAction('edit')}>
          <EditIcon sx={{ mr: 1 }} />
          Edit Project
        </MenuItem>
        <MenuItem onClick={() => handleProjectAction('delete')}>
          <DeleteIcon sx={{ mr: 1 }} />
          Delete Project
        </MenuItem>
      </Menu>
    </Box>
  );
}
