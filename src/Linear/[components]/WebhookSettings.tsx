/**
 * Linear Webhook Settings Component
 *
 * Component for managing Linear webhook configuration
 */

import React, { useState } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  Typography,
  TextField,
  Button,
  Box,
  Grid,
  FormControlLabel,
  Switch,
  Alert,
  Chip,
  Stack,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import {
  ContentCopy as CopyIcon,
  Info as InfoIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';

import { generateWebhookUrl, getWebhookSetupInstructions } from '../[services]/webhookEndpoint';

interface WebhookSettingsProps {
  webhookUrl?: string;
  webhookSecret?: string;
  webhookEnabled?: boolean;
  onWebhookUrlChange?: (url: string) => void;
  onWebhookSecretChange?: (secret: string) => void;
  onWebhookEnabledChange?: (enabled: boolean) => void;
  onTestWebhook?: () => Promise<boolean>;
}

export default function WebhookSettings({
  webhookUrl = '',
  webhookSecret = '',
  webhookEnabled = false,
  onWebhookUrlChange,
  onWebhookSecretChange,
  onWebhookEnabledChange,
  onTestWebhook
}: WebhookSettingsProps) {
  const [localWebhookUrl, setLocalWebhookUrl] = useState(webhookUrl);
  const [localWebhookSecret, setLocalWebhookSecret] = useState(webhookSecret);
  const [showInstructions, setShowInstructions] = useState(false);
  const [testResult, setTestResult] = useState<{ success: boolean; message: string } | null>(null);
  const [testing, setTesting] = useState(false);
  const [copied, setCopied] = useState(false);

  const handleWebhookUrlChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const url = event.target.value;
    setLocalWebhookUrl(url);
    onWebhookUrlChange?.(url);
  };

  const handleWebhookSecretChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const secret = event.target.value;
    setLocalWebhookSecret(secret);
    onWebhookSecretChange?.(secret);
  };

  const handleWebhookEnabledChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const enabled = event.target.checked;
    onWebhookEnabledChange?.(enabled);
  };

  const handleCopyUrl = async () => {
    if (localWebhookUrl) {
      try {
        await navigator.clipboard.writeText(localWebhookUrl);
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      } catch (error) {
        console.error('Failed to copy URL:', error);
      }
    }
  };

  const handleGenerateUrl = () => {
    const baseUrl = window.location.origin;
    const generatedUrl = generateWebhookUrl(baseUrl);
    setLocalWebhookUrl(generatedUrl);
    onWebhookUrlChange?.(generatedUrl);
  };

  const handleTestWebhook = async () => {
    if (!onTestWebhook) return;

    setTesting(true);
    setTestResult(null);

    try {
      const success = await onTestWebhook();
      setTestResult({
        success,
        message: success ? 'Webhook test successful!' : 'Webhook test failed'
      });
    } catch (error) {
      setTestResult({
        success: false,
        message: error instanceof Error ? error.message : 'Webhook test failed'
      });
    } finally {
      setTesting(false);
    }
  };

  const getWebhookStatus = (): { status: 'success' | 'warning' | 'error'; message: string } => {
    if (!webhookEnabled) {
      return { status: 'warning', message: 'Webhooks disabled' };
    }
    if (!localWebhookUrl) {
      return { status: 'error', message: 'No webhook URL configured' };
    }
    return { status: 'success', message: 'Webhooks enabled' };
  };

  const webhookStatus = getWebhookStatus();

  return (
    <>
      <Card>
        <CardHeader
          title="Webhook Configuration"
          subheader="Configure webhooks to receive real-time updates from Linear"
          action={
            <Chip
              icon={webhookStatus.status === 'success' ? <CheckCircleIcon /> : <ErrorIcon />}
              label={webhookStatus.message}
              color={webhookStatus.status}
              variant="outlined"
            />
          }
        />
        <CardContent>
          <Grid container spacing={3}>
            {/* Enable Webhooks */}
            <Grid item xs={12}>
              <FormControlLabel
                control={<Switch checked={webhookEnabled} onChange={handleWebhookEnabledChange} />}
                label="Enable Linear Webhooks"
              />
              <Typography variant="body2" color="text.secondary">
                Receive real-time updates when Linear issues and projects change
              </Typography>
            </Grid>

            {/* Webhook URL */}
            <Grid item xs={12}>
              <Box display="flex" gap={1} alignItems="flex-end">
                <TextField
                  fullWidth
                  label="Webhook URL"
                  value={localWebhookUrl}
                  onChange={handleWebhookUrlChange}
                  disabled={!webhookEnabled}
                  placeholder="https://your-domain.com/webhooks/linear"
                  helperText="The URL where Linear will send webhook events"
                />
                <Tooltip title={copied ? 'Copied!' : 'Copy URL'}>
                  <IconButton onClick={handleCopyUrl} disabled={!localWebhookUrl || !webhookEnabled}>
                    <CopyIcon />
                  </IconButton>
                </Tooltip>
                <Button variant="outlined" onClick={handleGenerateUrl} disabled={!webhookEnabled}>
                  Generate
                </Button>
              </Box>
            </Grid>

            {/* Webhook Secret */}
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Webhook Secret (Optional)"
                type="password"
                value={localWebhookSecret}
                onChange={handleWebhookSecretChange}
                disabled={!webhookEnabled}
                placeholder="Enter a secret for webhook validation"
                helperText="Optional secret for validating webhook authenticity"
              />
            </Grid>

            {/* Test Webhook */}
            <Grid item xs={12}>
              <Stack direction="row" spacing={2} alignItems="center">
                <Button
                  variant="outlined"
                  onClick={handleTestWebhook}
                  disabled={!webhookEnabled || !localWebhookUrl || testing || !onTestWebhook}
                  startIcon={testing ? <RefreshIcon /> : <CheckCircleIcon />}
                >
                  {testing ? 'Testing...' : 'Test Webhook'}
                </Button>
                <Button variant="text" startIcon={<InfoIcon />} onClick={() => setShowInstructions(true)}>
                  Setup Instructions
                </Button>
              </Stack>
            </Grid>

            {/* Test Result */}
            {testResult && (
              <Grid item xs={12}>
                <Alert severity={testResult.success ? 'success' : 'error'}>{testResult.message}</Alert>
              </Grid>
            )}

            {/* Webhook Events */}
            {webhookEnabled && (
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                  Supported Events
                </Typography>
                <Stack direction="row" spacing={1} flexWrap="wrap" gap={1}>
                  <Chip label="Issue Created" size="small" variant="outlined" />
                  <Chip label="Issue Updated" size="small" variant="outlined" />
                  <Chip label="Issue Deleted" size="small" variant="outlined" />
                  <Chip label="Project Created" size="small" variant="outlined" />
                  <Chip label="Project Updated" size="small" variant="outlined" />
                  <Chip label="Project Deleted" size="small" variant="outlined" />
                  <Chip label="Comment Created" size="small" variant="outlined" />
                  <Chip label="Comment Updated" size="small" variant="outlined" />
                  <Chip label="Comment Deleted" size="small" variant="outlined" />
                </Stack>
              </Grid>
            )}
          </Grid>
        </CardContent>
      </Card>

      {/* Setup Instructions Dialog */}
      <Dialog open={showInstructions} onClose={() => setShowInstructions(false)} maxWidth="md" fullWidth>
        <DialogTitle>Linear Webhook Setup Instructions</DialogTitle>
        <DialogContent>
          <Typography variant="body1" paragraph>
            Follow these steps to configure webhooks in Linear:
          </Typography>

          <Box component="ol" sx={{ pl: 2 }}>
            <Box component="li" sx={{ mb: 1 }}>
              <Typography variant="body2">Go to your Linear workspace settings</Typography>
            </Box>
            <Box component="li" sx={{ mb: 1 }}>
              <Typography variant="body2">
                Navigate to <strong>API → Webhooks</strong>
              </Typography>
            </Box>
            <Box component="li" sx={{ mb: 1 }}>
              <Typography variant="body2">
                Click <strong>"Create webhook"</strong>
              </Typography>
            </Box>
            <Box component="li" sx={{ mb: 1 }}>
              <Typography variant="body2">
                Set the URL to: <code>{localWebhookUrl || 'your-webhook-url'}</code>
              </Typography>
            </Box>
            <Box component="li" sx={{ mb: 1 }}>
              <Typography variant="body2">Select the events you want to receive (see supported events above)</Typography>
            </Box>
            <Box component="li" sx={{ mb: 1 }}>
              <Typography variant="body2">If using a webhook secret, enter it in the Linear webhook configuration</Typography>
            </Box>
            <Box component="li" sx={{ mb: 1 }}>
              <Typography variant="body2">Save the webhook configuration</Typography>
            </Box>
          </Box>

          <Alert severity="info" sx={{ mt: 2 }}>
            <Typography variant="body2">
              <strong>Note:</strong> Your webhook endpoint must be publicly accessible and return a 200 status code for Linear to consider
              it valid.
            </Typography>
          </Alert>

          {localWebhookSecret && (
            <Alert severity="warning" sx={{ mt: 1 }}>
              <Typography variant="body2">
                <strong>Security:</strong> Keep your webhook secret secure and don't share it publicly. It's used to verify that webhook
                requests are actually from Linear.
              </Typography>
            </Alert>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowInstructions(false)}>Close</Button>
          {localWebhookUrl && (
            <Button variant="contained" onClick={handleCopyUrl} startIcon={<CopyIcon />}>
              Copy Webhook URL
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </>
  );
}
