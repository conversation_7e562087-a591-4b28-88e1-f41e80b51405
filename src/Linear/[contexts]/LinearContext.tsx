/**
 * Linear Authentication Context
 *
 * React context for managing Linear authentication state and configuration
 */

import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { linearService } from '../[services]/linearService';
import { LinearConfig, LinearUser, LinearTeam, LinearIntegrationSettings } from '../[types]/Linear';

// Context State Interface
interface LinearContextState {
  isInitialized: boolean;
  isAuthenticated: boolean;
  isLoading: boolean;
  currentUser: LinearUser | null;
  teams: LinearTeam[];
  selectedTeamId: string | null;
  config: LinearConfig | null;
  settings: LinearIntegrationSettings | null;
  error: string | null;
}

// Context Actions
type LinearAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_INITIALIZED'; payload: boolean }
  | { type: 'SET_AUTHENTICATED'; payload: boolean }
  | { type: 'SET_CURRENT_USER'; payload: LinearUser | null }
  | { type: 'SET_TEAMS'; payload: LinearTeam[] }
  | { type: 'SET_SELECTED_TEAM'; payload: string | null }
  | { type: 'SET_CONFIG'; payload: LinearConfig | null }
  | { type: 'SET_SETTINGS'; payload: LinearIntegrationSettings | null }
  | { type: 'RESET_STATE' };

// Initial State
const initialState: LinearContextState = {
  isInitialized: false,
  isAuthenticated: false,
  isLoading: false,
  currentUser: null,
  teams: [],
  selectedTeamId: null,
  config: null,
  settings: null,
  error: null
};

// Reducer
function linearReducer(state: LinearContextState, action: LinearAction): LinearContextState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload, isLoading: false };
    case 'SET_INITIALIZED':
      return { ...state, isInitialized: action.payload };
    case 'SET_AUTHENTICATED':
      return { ...state, isAuthenticated: action.payload };
    case 'SET_CURRENT_USER':
      return { ...state, currentUser: action.payload };
    case 'SET_TEAMS':
      return { ...state, teams: action.payload };
    case 'SET_SELECTED_TEAM':
      return { ...state, selectedTeamId: action.payload };
    case 'SET_CONFIG':
      return { ...state, config: action.payload };
    case 'SET_SETTINGS':
      return { ...state, settings: action.payload };
    case 'RESET_STATE':
      return { ...initialState };
    default:
      return state;
  }
}

// Context Interface
interface LinearContextValue extends LinearContextState {
  // Authentication methods
  initialize: (apiKey: string) => Promise<void>;
  logout: () => void;

  // Team management
  selectTeam: (teamId: string) => void;
  refreshTeams: () => Promise<void>;

  // Settings management
  updateSettings: (settings: Partial<LinearIntegrationSettings>) => Promise<void>;

  // Utility methods
  isTeamSelected: () => boolean;
  getSelectedTeam: () => LinearTeam | null;
}

// Create Context
const LinearContext = createContext<LinearContextValue | undefined>(undefined);

// Storage keys
const STORAGE_KEYS = {
  API_KEY: 'linear_api_key',
  SELECTED_TEAM: 'linear_selected_team',
  SETTINGS: 'linear_settings'
};

// Provider Props
interface LinearProviderProps {
  children: ReactNode;
}

// Provider Component
export function LinearProvider({ children }: LinearProviderProps) {
  const [state, dispatch] = useReducer(linearReducer, initialState);

  // Initialize from localStorage on mount
  useEffect(() => {
    const initializeFromStorage = async () => {
      try {
        const storedApiKey = localStorage.getItem(STORAGE_KEYS.API_KEY);
        const storedTeamId = localStorage.getItem(STORAGE_KEYS.SELECTED_TEAM);
        const storedSettings = localStorage.getItem(STORAGE_KEYS.SETTINGS);

        if (storedSettings) {
          const settings: LinearIntegrationSettings = JSON.parse(storedSettings);
          dispatch({ type: 'SET_SETTINGS', payload: settings });
        }

        if (storedApiKey && storedApiKey.trim()) {
          await initialize(storedApiKey);

          if (storedTeamId) {
            dispatch({ type: 'SET_SELECTED_TEAM', payload: storedTeamId });
          }
        }
      } catch (error) {
        console.error('Error initializing Linear from storage:', error);
        dispatch({ type: 'SET_ERROR', payload: 'Failed to initialize from stored credentials' });
      }
    };

    initializeFromStorage();
  }, []);

  // Initialize Linear client
  const initialize = async (apiKey: string): Promise<void> => {
    dispatch({ type: 'SET_LOADING', payload: true });
    dispatch({ type: 'SET_ERROR', payload: null });

    try {
      const config: LinearConfig = {
        apiKey: apiKey.trim()
      };

      await linearService.initialize(config);

      // Get current user
      const currentUser = await linearService.getCurrentUser();

      // Get teams
      const teams = await linearService.getTeams();

      // Update state
      dispatch({ type: 'SET_CONFIG', payload: config });
      dispatch({ type: 'SET_CURRENT_USER', payload: currentUser });
      dispatch({ type: 'SET_TEAMS', payload: teams });
      dispatch({ type: 'SET_AUTHENTICATED', payload: true });
      dispatch({ type: 'SET_INITIALIZED', payload: true });

      // Store API key
      localStorage.setItem(STORAGE_KEYS.API_KEY, apiKey);

      // Auto-select first team if none selected
      if (teams.length > 0 && !state.selectedTeamId) {
        selectTeam(teams[0].id);
      }
    } catch (error) {
      console.error('Linear initialization error:', error);
      dispatch({ type: 'SET_ERROR', payload: error instanceof Error ? error.message : 'Authentication failed' });
      dispatch({ type: 'SET_AUTHENTICATED', payload: false });

      // Clear stored credentials on auth failure
      localStorage.removeItem(STORAGE_KEYS.API_KEY);
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  // Logout
  const logout = (): void => {
    dispatch({ type: 'RESET_STATE' });
    localStorage.removeItem(STORAGE_KEYS.API_KEY);
    localStorage.removeItem(STORAGE_KEYS.SELECTED_TEAM);
  };

  // Select team
  const selectTeam = (teamId: string): void => {
    dispatch({ type: 'SET_SELECTED_TEAM', payload: teamId });
    localStorage.setItem(STORAGE_KEYS.SELECTED_TEAM, teamId);
  };

  // Refresh teams
  const refreshTeams = async (): Promise<void> => {
    if (!state.isAuthenticated) return;

    dispatch({ type: 'SET_LOADING', payload: true });
    try {
      const teams = await linearService.getTeams();
      dispatch({ type: 'SET_TEAMS', payload: teams });
    } catch (error) {
      console.error('Error refreshing teams:', error);
      dispatch({ type: 'SET_ERROR', payload: 'Failed to refresh teams' });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  // Update settings
  const updateSettings = async (newSettings: Partial<LinearIntegrationSettings>): Promise<void> => {
    const updatedSettings: LinearIntegrationSettings = {
      ...state.settings,
      ...newSettings
    } as LinearIntegrationSettings;

    dispatch({ type: 'SET_SETTINGS', payload: updatedSettings });
    localStorage.setItem(STORAGE_KEYS.SETTINGS, JSON.stringify(updatedSettings));
  };

  // Utility methods
  const isTeamSelected = (): boolean => {
    return !!state.selectedTeamId;
  };

  const getSelectedTeam = (): LinearTeam | null => {
    if (!state.selectedTeamId) return null;
    return state.teams.find((team) => team.id === state.selectedTeamId) || null;
  };

  // Context value
  const contextValue: LinearContextValue = {
    ...state,
    initialize,
    logout,
    selectTeam,
    refreshTeams,
    updateSettings,
    isTeamSelected,
    getSelectedTeam
  };

  return <LinearContext.Provider value={contextValue}>{children}</LinearContext.Provider>;
}

// Hook to use Linear context
export function useLinear(): LinearContextValue {
  const context = useContext(LinearContext);
  if (context === undefined) {
    throw new Error('useLinear must be used within a LinearProvider');
  }
  return context;
}

// Hook to check if Linear is configured
export function useLinearAuth(): {
  isConfigured: boolean;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
} {
  const { isInitialized, isAuthenticated, isLoading, error } = useLinear();

  return {
    isConfigured: isInitialized,
    isAuthenticated,
    isLoading,
    error
  };
}

export default LinearContext;
