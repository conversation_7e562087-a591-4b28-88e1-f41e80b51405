/**
 * Linear Issues Page
 *
 * Main page for managing Linear issues
 */

import React, { useState } from 'react';
import { Box, Container, Dialog, DialogTitle, DialogContent, DialogActions, Button, Typography, Alert } from '@mui/material';

import { useLinear } from '../[contexts]/LinearContext';
import IssueList from '../[components]/IssueList';
import IssueForm from '../[components]/IssueForm';
import { LinearIssue } from '../[types]/Linear';
import { linearService } from '../[services]/linearService';

export default function IssuesPage() {
  const { isAuthenticated, isLoading, error, selectedTeamId } = useLinear();
  const [selectedIssue, setSelectedIssue] = useState<LinearIssue | null>(null);
  const [formMode, setFormMode] = useState<'create' | 'edit'>('create');
  const [showForm, setShowForm] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);

  const handleIssueSelect = (issue: LinearIssue) => {
    // For now, just edit the issue. Later we can add a detailed view
    handleIssueEdit(issue);
  };

  const handleIssueCreate = () => {
    setSelectedIssue(null);
    setFormMode('create');
    setShowForm(true);
  };

  const handleIssueEdit = (issue: LinearIssue) => {
    setSelectedIssue(issue);
    setFormMode('edit');
    setShowForm(true);
  };

  const handleIssueDelete = (issue: LinearIssue) => {
    setSelectedIssue(issue);
    setShowDeleteDialog(true);
  };

  const handleFormSubmit = (issue: LinearIssue) => {
    setShowForm(false);
    setRefreshKey((prev) => prev + 1); // Trigger refresh
  };

  const handleFormClose = () => {
    setShowForm(false);
    setSelectedIssue(null);
  };

  const handleDeleteConfirm = async () => {
    if (!selectedIssue) return;

    setDeleteLoading(true);
    try {
      await linearService.deleteIssue(selectedIssue.id);
      setShowDeleteDialog(false);
      setSelectedIssue(null);
      setRefreshKey((prev) => prev + 1); // Trigger refresh
    } catch (error) {
      console.error('Error deleting issue:', error);
      // You might want to show an error message here
    } finally {
      setDeleteLoading(false);
    }
  };

  const handleDeleteCancel = () => {
    setShowDeleteDialog(false);
    setSelectedIssue(null);
  };

  if (isLoading) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight={400}>
          <Typography>Loading Linear integration...</Typography>
        </Box>
      </Container>
    );
  }

  if (!isAuthenticated) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="warning">Please configure Linear integration in settings to view issues.</Alert>
      </Container>
    );
  }

  if (!selectedTeamId) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="info">Please select a team to view issues.</Alert>
      </Container>
    );
  }

  if (error) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="error">{error}</Alert>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Main Content */}
      <IssueList
        key={refreshKey}
        onIssueSelect={handleIssueSelect}
        onIssueCreate={handleIssueCreate}
        onIssueEdit={handleIssueEdit}
        onIssueDelete={handleIssueDelete}
      />

      {/* Issue Form Dialog */}
      <IssueForm open={showForm} onClose={handleFormClose} onSubmit={handleFormSubmit} issue={selectedIssue} mode={formMode} />

      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteDialog} onClose={handleDeleteCancel} maxWidth="sm" fullWidth>
        <DialogTitle>Delete Issue</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete the issue "{selectedIssue?.identifier}: {selectedIssue?.title}"?
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteCancel} disabled={deleteLoading}>
            Cancel
          </Button>
          <Button onClick={handleDeleteConfirm} color="error" variant="contained" disabled={deleteLoading}>
            {deleteLoading ? 'Deleting...' : 'Delete Issue'}
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
}
