/**
 * Linear Dashboard
 *
 * Dashboard showing Linear project progress, issue metrics, and team performance
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Grid,
  Card,
  CardContent,
  Typography,
  LinearProgress,
  Chip,
  Avatar,
  AvatarGroup,
  Alert,
  CircularProgress,
  Stack,
  Divider
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  Assignment as AssignmentIcon,
  CheckCircle as CheckCircleIcon,
  Schedule as ScheduleIcon,
  Group as GroupIcon,
  Flag as FlagIcon
} from '@mui/icons-material';
import Chart from 'react-apexcharts';
import { ApexOptions } from 'apexcharts';

import { useLinear } from '../[contexts]/LinearContext';
import { linearService } from '../[services]/linearService';
import { LinearProject, LinearIssue, LinearTeam, IssuePriority, WorkflowStateType } from '../[types]/Linear';

interface DashboardMetrics {
  totalIssues: number;
  completedIssues: number;
  inProgressIssues: number;
  backlogIssues: number;
  totalProjects: number;
  activeProjects: number;
  completedProjects: number;
  teamMembers: number;
  avgCompletionTime: number;
}

interface IssuesByPriority {
  urgent: number;
  high: number;
  medium: number;
  low: number;
  noPriority: number;
}

interface IssuesByState {
  [key: string]: number;
}

export default function LinearDashboard() {
  const { isAuthenticated, isLoading, error, selectedTeamId, teams } = useLinear();
  const [metrics, setMetrics] = useState<DashboardMetrics | null>(null);
  const [projects, setProjects] = useState<LinearProject[]>([]);
  const [issues, setIssues] = useState<LinearIssue[]>([]);
  const [issuesByPriority, setIssuesByPriority] = useState<IssuesByPriority | null>(null);
  const [issuesByState, setIssuesByState] = useState<IssuesByState | null>(null);
  const [loading, setLoading] = useState(false);
  const [dashboardError, setDashboardError] = useState<string | null>(null);

  useEffect(() => {
    if (isAuthenticated && selectedTeamId) {
      loadDashboardData();
    }
  }, [isAuthenticated, selectedTeamId]);

  const loadDashboardData = async () => {
    setLoading(true);
    setDashboardError(null);
    try {
      const [projectList, issueList] = await Promise.all([
        linearService.getProjects({ teamIds: [selectedTeamId!] }),
        linearService.getIssues({ teamId: selectedTeamId! })
      ]);

      setProjects(projectList);
      setIssues(issueList);
      calculateMetrics(projectList, issueList);
      calculateIssueDistribution(issueList);
    } catch (err) {
      setDashboardError(err instanceof Error ? err.message : 'Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const calculateMetrics = (projectList: LinearProject[], issueList: LinearIssue[]) => {
    const completedIssues = issueList.filter((issue) => issue.state.type === WorkflowStateType.COMPLETED).length;

    const inProgressIssues = issueList.filter((issue) => issue.state.type === WorkflowStateType.STARTED).length;

    const backlogIssues = issueList.filter(
      (issue) => issue.state.type === WorkflowStateType.BACKLOG || issue.state.type === WorkflowStateType.UNSTARTED
    ).length;

    const activeProjects = projectList.filter((project) => project.state === 'started' || project.state === 'planned').length;

    const completedProjects = projectList.filter((project) => project.state === 'completed').length;

    // Get unique team members across all projects
    const allMembers = new Set();
    projectList.forEach((project) => {
      project.members.forEach((member) => allMembers.add(member.id));
    });

    const metrics: DashboardMetrics = {
      totalIssues: issueList.length,
      completedIssues,
      inProgressIssues,
      backlogIssues,
      totalProjects: projectList.length,
      activeProjects,
      completedProjects,
      teamMembers: allMembers.size,
      avgCompletionTime: 0 // TODO: Calculate based on issue completion times
    };

    setMetrics(metrics);
  };

  const calculateIssueDistribution = (issueList: LinearIssue[]) => {
    // Issues by priority
    const priorityDistribution: IssuesByPriority = {
      urgent: issueList.filter((issue) => issue.priority === IssuePriority.URGENT).length,
      high: issueList.filter((issue) => issue.priority === IssuePriority.HIGH).length,
      medium: issueList.filter((issue) => issue.priority === IssuePriority.MEDIUM).length,
      low: issueList.filter((issue) => issue.priority === IssuePriority.LOW).length,
      noPriority: issueList.filter((issue) => issue.priority === IssuePriority.NO_PRIORITY).length
    };

    // Issues by state
    const stateDistribution: IssuesByState = {};
    issueList.forEach((issue) => {
      const stateName = issue.state.name;
      stateDistribution[stateName] = (stateDistribution[stateName] || 0) + 1;
    });

    setIssuesByPriority(priorityDistribution);
    setIssuesByState(stateDistribution);
  };

  const getSelectedTeam = (): LinearTeam | null => {
    return teams.find((team) => team.id === selectedTeamId) || null;
  };

  const getPriorityChartOptions = (): ApexOptions => {
    return {
      chart: {
        type: 'donut',
        height: 300
      },
      labels: ['Urgent', 'High', 'Medium', 'Low', 'No Priority'],
      colors: ['#f44336', '#ff9800', '#2196f3', '#4caf50', '#9e9e9e'],
      legend: {
        position: 'bottom'
      },
      responsive: [
        {
          breakpoint: 480,
          options: {
            chart: {
              width: 200
            },
            legend: {
              position: 'bottom'
            }
          }
        }
      ]
    };
  };

  const getStateChartOptions = (): ApexOptions => {
    return {
      chart: {
        type: 'bar',
        height: 300
      },
      plotOptions: {
        bar: {
          horizontal: false,
          columnWidth: '55%'
        }
      },
      dataLabels: {
        enabled: false
      },
      xaxis: {
        categories: issuesByState ? Object.keys(issuesByState) : []
      },
      colors: ['#1976d2']
    };
  };

  if (isLoading) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight={400}>
          <Typography>Loading Linear integration...</Typography>
        </Box>
      </Container>
    );
  }

  if (!isAuthenticated) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="warning">Please configure Linear integration in settings to view dashboard.</Alert>
      </Container>
    );
  }

  if (!selectedTeamId) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="info">Please select a team to view dashboard.</Alert>
      </Container>
    );
  }

  if (error || dashboardError) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="error">{error || dashboardError}</Alert>
      </Container>
    );
  }

  const selectedTeam = getSelectedTeam();

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Header */}
      <Box mb={4}>
        <Typography variant="h4" component="h1" gutterBottom>
          Linear Dashboard
        </Typography>
        {selectedTeam && (
          <Typography variant="h6" color="text.secondary">
            {selectedTeam.name} Team
          </Typography>
        )}
      </Box>

      {loading && <LinearProgress sx={{ mb: 3 }} />}

      {metrics && (
        <>
          {/* Key Metrics */}
          <Grid container spacing={3} mb={4}>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Box display="flex" alignItems="center" justifyContent="space-between">
                    <Box>
                      <Typography color="text.secondary" gutterBottom>
                        Total Issues
                      </Typography>
                      <Typography variant="h4" component="div">
                        {metrics.totalIssues}
                      </Typography>
                    </Box>
                    <AssignmentIcon color="primary" sx={{ fontSize: 40 }} />
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Box display="flex" alignItems="center" justifyContent="space-between">
                    <Box>
                      <Typography color="text.secondary" gutterBottom>
                        Completed
                      </Typography>
                      <Typography variant="h4" component="div">
                        {metrics.completedIssues}
                      </Typography>
                    </Box>
                    <CheckCircleIcon color="success" sx={{ fontSize: 40 }} />
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Box display="flex" alignItems="center" justifyContent="space-between">
                    <Box>
                      <Typography color="text.secondary" gutterBottom>
                        In Progress
                      </Typography>
                      <Typography variant="h4" component="div">
                        {metrics.inProgressIssues}
                      </Typography>
                    </Box>
                    <ScheduleIcon color="warning" sx={{ fontSize: 40 }} />
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Box display="flex" alignItems="center" justifyContent="space-between">
                    <Box>
                      <Typography color="text.secondary" gutterBottom>
                        Active Projects
                      </Typography>
                      <Typography variant="h4" component="div">
                        {metrics.activeProjects}
                      </Typography>
                    </Box>
                    <TrendingUpIcon color="info" sx={{ fontSize: 40 }} />
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Charts */}
          <Grid container spacing={3} mb={4}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Issues by Priority
                  </Typography>
                  {issuesByPriority && (
                    <Chart
                      options={getPriorityChartOptions()}
                      series={[
                        issuesByPriority.urgent,
                        issuesByPriority.high,
                        issuesByPriority.medium,
                        issuesByPriority.low,
                        issuesByPriority.noPriority
                      ]}
                      type="donut"
                      height={300}
                    />
                  )}
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Issues by State
                  </Typography>
                  {issuesByState && (
                    <Chart
                      options={getStateChartOptions()}
                      series={[
                        {
                          name: 'Issues',
                          data: Object.values(issuesByState)
                        }
                      ]}
                      type="bar"
                      height={300}
                    />
                  )}
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Project Overview */}
          <Grid container spacing={3}>
            <Grid item xs={12} md={8}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Active Projects
                  </Typography>
                  <Stack spacing={2}>
                    {projects
                      .filter((project) => project.state === 'started' || project.state === 'planned')
                      .slice(0, 5)
                      .map((project) => (
                        <Box key={project.id}>
                          <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                            <Typography variant="subtitle1" fontWeight="medium">
                              {project.icon} {project.name}
                            </Typography>
                            <Chip label={project.state} size="small" color={project.state === 'started' ? 'primary' : 'info'} />
                          </Box>
                          <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                            <Typography variant="body2" color="text.secondary">
                              Progress: {Math.round(project.progress * 100)}%
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              {project.completedIssueCount}/{project.issueCount} issues
                            </Typography>
                          </Box>
                          <LinearProgress variant="determinate" value={project.progress * 100} sx={{ height: 6, borderRadius: 3 }} />
                          {project.members.length > 0 && (
                            <Box mt={1}>
                              <AvatarGroup max={5} sx={{ justifyContent: 'flex-start' }}>
                                {project.members.map((member) => (
                                  <Avatar key={member.id} src={member.avatarUrl} alt={member.displayName} sx={{ width: 24, height: 24 }}>
                                    {member.displayName.charAt(0)}
                                  </Avatar>
                                ))}
                              </AvatarGroup>
                            </Box>
                          )}
                          <Divider sx={{ mt: 2 }} />
                        </Box>
                      ))}
                  </Stack>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={4}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Team Summary
                  </Typography>
                  <Stack spacing={2}>
                    <Box display="flex" justifyContent="space-between">
                      <Typography variant="body2">Total Projects:</Typography>
                      <Typography variant="body2" fontWeight="medium">
                        {metrics.totalProjects}
                      </Typography>
                    </Box>
                    <Box display="flex" justifyContent="space-between">
                      <Typography variant="body2">Completed Projects:</Typography>
                      <Typography variant="body2" fontWeight="medium">
                        {metrics.completedProjects}
                      </Typography>
                    </Box>
                    <Box display="flex" justifyContent="space-between">
                      <Typography variant="body2">Team Members:</Typography>
                      <Typography variant="body2" fontWeight="medium">
                        {metrics.teamMembers}
                      </Typography>
                    </Box>
                    <Box display="flex" justifyContent="space-between">
                      <Typography variant="body2">Backlog Issues:</Typography>
                      <Typography variant="body2" fontWeight="medium">
                        {metrics.backlogIssues}
                      </Typography>
                    </Box>
                    <Divider />
                    <Box>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Completion Rate
                      </Typography>
                      <Box display="flex" alignItems="center" gap={1}>
                        <LinearProgress
                          variant="determinate"
                          value={metrics.totalIssues > 0 ? (metrics.completedIssues / metrics.totalIssues) * 100 : 0}
                          sx={{ flexGrow: 1, height: 8, borderRadius: 4 }}
                        />
                        <Typography variant="body2" fontWeight="medium">
                          {metrics.totalIssues > 0 ? Math.round((metrics.completedIssues / metrics.totalIssues) * 100) : 0}%
                        </Typography>
                      </Box>
                    </Box>
                  </Stack>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </>
      )}
    </Container>
  );
}
