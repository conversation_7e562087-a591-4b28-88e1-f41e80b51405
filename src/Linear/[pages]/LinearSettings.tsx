/**
 * Linear Settings Page
 *
 * Configuration page for Linear integration settings
 */

import React, { useState, useEffect } from 'react';
import {
  Container,
  Card,
  CardContent,
  CardHeader,
  Typography,
  TextField,
  Button,
  Box,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  Alert,
  CircularProgress,
  Divider,
  Stack,
  Chip
} from '@mui/material';
import { Save as SaveIcon, Refresh as RefreshIcon, CheckCircle as CheckCircleIcon, Error as ErrorIcon } from '@mui/icons-material';

import { useLinear } from '../[contexts]/LinearContext';
import { LinearIntegrationSettings, LinearTeam } from '../[types]/Linear';
import WebhookSettings from '../[components]/WebhookSettings';

export default function LinearSettings() {
  const {
    isAuthenticated,
    isLoading,
    error,
    currentUser,
    teams,
    selectedTeamId,
    settings,
    initialize,
    logout,
    selectTeam,
    refreshTeams,
    updateSettings
  } = useLinear();

  const [apiKey, setApiKey] = useState('');
  const [localSettings, setLocalSettings] = useState<Partial<LinearIntegrationSettings>>({
    enabled: true,
    syncEnabled: false,
    autoCreateIssues: false,
    autoAssignUsers: false,
    syncLabels: true,
    syncComments: true
  });
  const [saving, setSaving] = useState(false);
  const [testing, setTesting] = useState(false);
  const [testResult, setTestResult] = useState<{ success: boolean; message: string } | null>(null);

  useEffect(() => {
    if (settings) {
      setLocalSettings(settings);
    }
  }, [settings]);

  const handleApiKeyChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setApiKey(event.target.value);
    setTestResult(null);
  };

  const handleSettingChange = (field: keyof LinearIntegrationSettings) => (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;
    setLocalSettings((prev) => ({
      ...prev,
      [field]: value
    }));
  };

  const handleTestConnection = async () => {
    if (!apiKey.trim()) {
      setTestResult({ success: false, message: 'Please enter an API key' });
      return;
    }

    setTesting(true);
    setTestResult(null);

    try {
      await initialize(apiKey);
      setTestResult({ success: true, message: 'Connection successful!' });
    } catch (error) {
      setTestResult({
        success: false,
        message: error instanceof Error ? error.message : 'Connection failed'
      });
    } finally {
      setTesting(false);
    }
  };

  const handleSaveSettings = async () => {
    setSaving(true);
    try {
      await updateSettings(localSettings);
      setTestResult({ success: true, message: 'Settings saved successfully!' });
    } catch (error) {
      setTestResult({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to save settings'
      });
    } finally {
      setSaving(false);
    }
  };

  const handleTeamChange = (teamId: string) => {
    selectTeam(teamId);
  };

  const handleRefreshTeams = async () => {
    try {
      await refreshTeams();
    } catch (error) {
      setTestResult({
        success: false,
        message: 'Failed to refresh teams'
      });
    }
  };

  const handleDisconnect = () => {
    logout();
    setApiKey('');
    setTestResult(null);
  };

  return (
    <Container maxWidth="md" sx={{ py: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Linear Integration Settings
      </Typography>
      <Typography variant="body1" color="text.secondary" paragraph>
        Configure your Linear integration to sync projects and issues with TRQ.
      </Typography>

      {/* Connection Status */}
      <Card sx={{ mb: 3 }}>
        <CardHeader
          title="Connection Status"
          action={
            isAuthenticated ? (
              <Chip icon={<CheckCircleIcon />} label="Connected" color="success" variant="outlined" />
            ) : (
              <Chip icon={<ErrorIcon />} label="Not Connected" color="error" variant="outlined" />
            )
          }
        />
        <CardContent>
          {isAuthenticated && currentUser && (
            <Box>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Connected as: <strong>{currentUser.displayName}</strong> ({currentUser.email})
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Teams available: {teams.length}
              </Typography>
            </Box>
          )}

          {error && (
            <Alert severity="error" sx={{ mt: 2 }}>
              {error}
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* API Configuration */}
      <Card sx={{ mb: 3 }}>
        <CardHeader title="API Configuration" />
        <CardContent>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Linear API Key"
                type="password"
                value={apiKey}
                onChange={handleApiKeyChange}
                placeholder="Enter your Linear API key"
                helperText="You can find your API key in Linear Settings > API"
              />
            </Grid>
            <Grid item xs={12}>
              <Stack direction="row" spacing={2}>
                <Button
                  variant="contained"
                  onClick={handleTestConnection}
                  disabled={!apiKey.trim() || testing || isLoading}
                  startIcon={testing ? <CircularProgress size={20} /> : <CheckCircleIcon />}
                >
                  {testing ? 'Testing...' : 'Test Connection'}
                </Button>
                {isAuthenticated && (
                  <Button variant="outlined" color="error" onClick={handleDisconnect}>
                    Disconnect
                  </Button>
                )}
              </Stack>
            </Grid>
            {testResult && (
              <Grid item xs={12}>
                <Alert severity={testResult.success ? 'success' : 'error'}>{testResult.message}</Alert>
              </Grid>
            )}
          </Grid>
        </CardContent>
      </Card>

      {/* Team Selection */}
      {isAuthenticated && teams.length > 0 && (
        <Card sx={{ mb: 3 }}>
          <CardHeader
            title="Team Selection"
            action={
              <Button size="small" startIcon={<RefreshIcon />} onClick={handleRefreshTeams}>
                Refresh
              </Button>
            }
          />
          <CardContent>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Selected Team</InputLabel>
                  <Select value={selectedTeamId || ''} onChange={(e) => handleTeamChange(e.target.value)} label="Selected Team">
                    {teams.map((team) => (
                      <MenuItem key={team.id} value={team.id}>
                        {team.name} ({team.key})
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                {selectedTeamId && (
                  <Box>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      Selected Team Info
                    </Typography>
                    {(() => {
                      const selectedTeam = teams.find((t) => t.id === selectedTeamId);
                      return selectedTeam ? (
                        <Box>
                          <Typography variant="body2">
                            <strong>{selectedTeam.name}</strong>
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {selectedTeam.issueCount} issues
                          </Typography>
                        </Box>
                      ) : null;
                    })()}
                  </Box>
                )}
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      )}

      {/* Integration Settings */}
      <Card sx={{ mb: 3 }}>
        <CardHeader title="Integration Settings" />
        <CardContent>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <FormControlLabel
                control={<Switch checked={localSettings.enabled || false} onChange={handleSettingChange('enabled')} />}
                label="Enable Linear Integration"
              />
            </Grid>

            <Grid item xs={12}>
              <Divider />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={localSettings.syncEnabled || false}
                    onChange={handleSettingChange('syncEnabled')}
                    disabled={!localSettings.enabled}
                  />
                }
                label="Enable Automatic Sync"
              />
              <Typography variant="body2" color="text.secondary">
                Automatically sync changes between Linear and TRQ
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={localSettings.autoCreateIssues || false}
                    onChange={handleSettingChange('autoCreateIssues')}
                    disabled={!localSettings.enabled}
                  />
                }
                label="Auto-create Issues"
              />
              <Typography variant="body2" color="text.secondary">
                Automatically create Linear issues from TRQ events
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={localSettings.autoAssignUsers || false}
                    onChange={handleSettingChange('autoAssignUsers')}
                    disabled={!localSettings.enabled}
                  />
                }
                label="Auto-assign Users"
              />
              <Typography variant="body2" color="text.secondary">
                Automatically assign issues based on TRQ user mappings
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={localSettings.syncLabels || false}
                    onChange={handleSettingChange('syncLabels')}
                    disabled={!localSettings.enabled}
                  />
                }
                label="Sync Labels"
              />
              <Typography variant="body2" color="text.secondary">
                Sync issue labels between Linear and TRQ
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={localSettings.syncComments || false}
                    onChange={handleSettingChange('syncComments')}
                    disabled={!localSettings.enabled}
                  />
                }
                label="Sync Comments"
              />
              <Typography variant="body2" color="text.secondary">
                Sync comments and updates between platforms
              </Typography>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Webhook Settings */}
      {isAuthenticated && (
        <WebhookSettings
          webhookUrl={localSettings.webhookUrl || ''}
          webhookEnabled={localSettings.syncEnabled || false}
          onWebhookUrlChange={(url) => setLocalSettings((prev) => ({ ...prev, webhookUrl: url }))}
          onWebhookEnabledChange={(enabled) => setLocalSettings((prev) => ({ ...prev, syncEnabled: enabled }))}
          onTestWebhook={async () => {
            // Implement webhook test logic here
            return true;
          }}
        />
      )}

      {/* Save Button */}
      <Box display="flex" justifyContent="flex-end" mt={3}>
        <Button
          variant="contained"
          size="large"
          startIcon={saving ? <CircularProgress size={20} /> : <SaveIcon />}
          onClick={handleSaveSettings}
          disabled={saving}
        >
          {saving ? 'Saving...' : 'Save Settings'}
        </Button>
      </Box>
    </Container>
  );
}
