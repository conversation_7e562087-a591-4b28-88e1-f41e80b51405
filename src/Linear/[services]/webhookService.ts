/**
 * Linear Webhook Service
 *
 * Service for handling Linear webhook events and syncing with TRQ
 */

import { LinearWebhookPayload, WebhookAction, WebhookType, LinearIssue, LinearProject, LinearComment } from '../[types]/Linear';

export interface WebhookHandler {
  handleIssueCreated: (issue: LinearIssue) => Promise<void>;
  handleIssueUpdated: (issue: LinearIssue) => Promise<void>;
  handleIssueDeleted: (issueId: string) => Promise<void>;
  handleProjectCreated: (project: LinearProject) => Promise<void>;
  handleProjectUpdated: (project: LinearProject) => Promise<void>;
  handleProjectDeleted: (projectId: string) => Promise<void>;
  handleCommentCreated: (comment: LinearComment) => Promise<void>;
  handleCommentUpdated: (comment: LinearComment) => Promise<void>;
  handleCommentDeleted: (commentId: string) => Promise<void>;
}

export class LinearWebhookService {
  private handlers: <PERSON>hookHandler[] = [];

  /**
   * Register a webhook handler
   */
  registerHandler(handler: WebhookHandler): void {
    this.handlers.push(handler);
  }

  /**
   * Unregister a webhook handler
   */
  unregisterHandler(handler: WebhookHandler): void {
    const index = this.handlers.indexOf(handler);
    if (index > -1) {
      this.handlers.splice(index, 1);
    }
  }

  /**
   * Process incoming webhook payload
   */
  async processWebhook(payload: LinearWebhookPayload): Promise<void> {
    try {
      console.log('Processing Linear webhook:', payload.type, payload.action);

      switch (payload.type) {
        case WebhookType.ISSUE:
          await this.handleIssueWebhook(payload);
          break;
        case WebhookType.PROJECT:
          await this.handleProjectWebhook(payload);
          break;
        case WebhookType.COMMENT:
          await this.handleCommentWebhook(payload);
          break;
        default:
          console.log('Unhandled webhook type:', payload.type);
      }
    } catch (error) {
      console.error('Error processing webhook:', error);
      throw error;
    }
  }

  /**
   * Handle issue webhook events
   */
  private async handleIssueWebhook(payload: LinearWebhookPayload): Promise<void> {
    const issue = payload.data as LinearIssue;

    switch (payload.action) {
      case WebhookAction.CREATE:
        await this.notifyHandlers((handler) => handler.handleIssueCreated(issue));
        break;
      case WebhookAction.UPDATE:
        await this.notifyHandlers((handler) => handler.handleIssueUpdated(issue));
        break;
      case WebhookAction.REMOVE:
        await this.notifyHandlers((handler) => handler.handleIssueDeleted(issue.id));
        break;
    }
  }

  /**
   * Handle project webhook events
   */
  private async handleProjectWebhook(payload: LinearWebhookPayload): Promise<void> {
    const project = payload.data as LinearProject;

    switch (payload.action) {
      case WebhookAction.CREATE:
        await this.notifyHandlers((handler) => handler.handleProjectCreated(project));
        break;
      case WebhookAction.UPDATE:
        await this.notifyHandlers((handler) => handler.handleProjectUpdated(project));
        break;
      case WebhookAction.REMOVE:
        await this.notifyHandlers((handler) => handler.handleProjectDeleted(project.id));
        break;
    }
  }

  /**
   * Handle comment webhook events
   */
  private async handleCommentWebhook(payload: LinearWebhookPayload): Promise<void> {
    const comment = payload.data as LinearComment;

    switch (payload.action) {
      case WebhookAction.CREATE:
        await this.notifyHandlers((handler) => handler.handleCommentCreated(comment));
        break;
      case WebhookAction.UPDATE:
        await this.notifyHandlers((handler) => handler.handleCommentUpdated(comment));
        break;
      case WebhookAction.REMOVE:
        await this.notifyHandlers((handler) => handler.handleCommentDeleted(comment.id));
        break;
    }
  }

  /**
   * Notify all registered handlers
   */
  private async notifyHandlers(action: (handler: WebhookHandler) => Promise<void>): Promise<void> {
    const promises = this.handlers.map(async (handler) => {
      try {
        await action(handler);
      } catch (error) {
        console.error('Error in webhook handler:', error);
      }
    });

    await Promise.all(promises);
  }

  /**
   * Validate webhook signature (if Linear provides one)
   */
  validateWebhookSignature(payload: string, signature: string, secret: string): boolean {
    // Linear webhook signature validation would go here
    // This is a placeholder implementation
    return true;
  }
}

/**
 * TRQ-specific webhook handler implementation
 */
export class TRQWebhookHandler implements WebhookHandler {
  async handleIssueCreated(issue: LinearIssue): Promise<void> {
    console.log('TRQ: Issue created:', issue.identifier, issue.title);

    // Create corresponding TRQ activity or notification
    // This could integrate with the Activity module
    try {
      // Example: Create activity log entry
      // await activityService.trackActivity(
      //   ActivityType.EXTERNAL_INTEGRATION,
      //   `Linear issue created: ${issue.identifier} - ${issue.title}`,
      //   ActivityStatus.COMPLETED,
      //   'linear_issue',
      //   issue.id,
      //   { linearIssue: issue }
      // );
    } catch (error) {
      console.error('Error creating TRQ activity for Linear issue:', error);
    }
  }

  async handleIssueUpdated(issue: LinearIssue): Promise<void> {
    console.log('TRQ: Issue updated:', issue.identifier, issue.title);

    // Update corresponding TRQ records or create notifications
    try {
      // Example: Update related TRQ entities or create notifications
      // if (issue.assignee) {
      //   await notificationService.createNotification({
      //     userId: issue.assignee.id,
      //     title: 'Linear Issue Updated',
      //     message: `Issue ${issue.identifier} has been updated`,
      //     type: 'linear_update',
      //     data: { issueId: issue.id }
      //   });
      // }
    } catch (error) {
      console.error('Error handling Linear issue update in TRQ:', error);
    }
  }

  async handleIssueDeleted(issueId: string): Promise<void> {
    console.log('TRQ: Issue deleted:', issueId);

    // Clean up corresponding TRQ records
    try {
      // Example: Remove related activities or notifications
      // await activityService.deleteActivitiesByResource('linear_issue', issueId);
    } catch (error) {
      console.error('Error cleaning up TRQ records for deleted Linear issue:', error);
    }
  }

  async handleProjectCreated(project: LinearProject): Promise<void> {
    console.log('TRQ: Project created:', project.name);

    // Create corresponding TRQ project or activity
    try {
      // Example: Create TRQ project or activity log
      // await activityService.trackActivity(
      //   ActivityType.EXTERNAL_INTEGRATION,
      //   `Linear project created: ${project.name}`,
      //   ActivityStatus.COMPLETED,
      //   'linear_project',
      //   project.id,
      //   { linearProject: project }
      // );
    } catch (error) {
      console.error('Error creating TRQ activity for Linear project:', error);
    }
  }

  async handleProjectUpdated(project: LinearProject): Promise<void> {
    console.log('TRQ: Project updated:', project.name);

    // Update corresponding TRQ records
    try {
      // Example: Update related TRQ entities
      // await projectService.syncWithLinearProject(project);
    } catch (error) {
      console.error('Error handling Linear project update in TRQ:', error);
    }
  }

  async handleProjectDeleted(projectId: string): Promise<void> {
    console.log('TRQ: Project deleted:', projectId);

    // Clean up corresponding TRQ records
    try {
      // Example: Remove related activities
      // await activityService.deleteActivitiesByResource('linear_project', projectId);
    } catch (error) {
      console.error('Error cleaning up TRQ records for deleted Linear project:', error);
    }
  }

  async handleCommentCreated(comment: LinearComment): Promise<void> {
    console.log('TRQ: Comment created on issue:', comment.issue.identifier);

    // Create corresponding TRQ notification or activity
    try {
      // Example: Create notification for relevant users
      // if (comment.issue.assignee) {
      //   await notificationService.createNotification({
      //     userId: comment.issue.assignee.id,
      //     title: 'New Comment on Linear Issue',
      //     message: `${comment.user.displayName} commented on ${comment.issue.identifier}`,
      //     type: 'linear_comment',
      //     data: { issueId: comment.issue.id, commentId: comment.id }
      //   });
      // }
    } catch (error) {
      console.error('Error creating TRQ notification for Linear comment:', error);
    }
  }

  async handleCommentUpdated(comment: LinearComment): Promise<void> {
    console.log('TRQ: Comment updated on issue:', comment.issue.identifier);

    // Handle comment updates if needed
    try {
      // Example: Update related notifications or activities
    } catch (error) {
      console.error('Error handling Linear comment update in TRQ:', error);
    }
  }

  async handleCommentDeleted(commentId: string): Promise<void> {
    console.log('TRQ: Comment deleted:', commentId);

    // Clean up corresponding TRQ records
    try {
      // Example: Remove related notifications
      // await notificationService.deleteNotificationsByData({ commentId });
    } catch (error) {
      console.error('Error cleaning up TRQ records for deleted Linear comment:', error);
    }
  }
}

// Export singleton instances
export const webhookService = new LinearWebhookService();
export const trqWebhookHandler = new TRQWebhookHandler();

// Register the TRQ handler by default
webhookService.registerHandler(trqWebhookHandler);
