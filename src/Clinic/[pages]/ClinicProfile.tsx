import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { Box, Typography, Card, CardContent, CardMedia, CircularProgress } from '@mui/material';
import { getClinicById } from '../[services]/clinicService';
import { Clinic } from '../[types]/Clinic';

const ClinicProfile = () => {
  const { id } = useParams<{ id: string }>();
  const [clinic, setClinic] = useState<Clinic | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!id) return;
    getClinicById(id).then((data) => {
      setClinic(data);
      setLoading(false);
    });
  }, [id]);

  if (loading) return <CircularProgress />;
  if (!clinic) return <Typography>Clinic not found</Typography>;

  return (
    <Box sx={{ maxWidth: 800, mx: 'auto', mt: 4 }}>
      <Card>
        {clinic.imageUrl && <CardMedia component="img" height="250" image={clinic.imageUrl} alt={clinic.name} />}
        <CardContent>
          <Typography variant="h4">{clinic.name}</Typography>
          <Typography variant="body1" sx={{ mt: 2 }}>
            {clinic.description}
          </Typography>
          <Typography variant="subtitle1" sx={{ mt: 2 }}>
            Address:{' '}
            {clinic.address
              ? `${clinic.address.street}, ${clinic.address.city}, ${clinic.address.state}, ${clinic.address.zipCode}, ${clinic.address.country}`
              : 'N/A'}
          </Typography>
          {/* Add more fields as needed */}
        </CardContent>
      </Card>
    </Box>
  );
};

export default ClinicProfile;
