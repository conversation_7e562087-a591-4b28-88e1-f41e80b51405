import { useNavigate } from 'react-router-dom';
import { useAuth } from 'Authentication/[contexts]/AuthContext';
import { getClinicById, getClinics } from '../[services]/clinicService';
import { Clinic } from '../[types]/Clinic';
import {
  Box,
  Typography,
  Button,
  Avatar,
  CircularProgress,
  Card,
  CardContent,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
  Alert,
  Snackbar
} from '@mui/material';
import BusinessIcon from '@mui/icons-material/Business';
import EmailIcon from '@mui/icons-material/Email';
import PhoneIcon from '@mui/icons-material/Phone';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import ROUTES from 'Routing/appRoutes';
import { useState, useEffect } from 'react';
import { Role } from 'RBAC/[types]/Role';
import { updateUser } from 'Users/[services]/userService';

/**
 * MyClinic Page
 * Shows the profile of the currently assigned clinic for the logged-in user.
 * Works for patients, clients, doctors, and clinic admins.
 * If no clinic is assigned, allows the user to select one from a dropdown.
 */
const MyClinic = () => {
  const navigate = useNavigate();
  const { userData, isLoading: userLoading, error: userError } = useAuth();
  const [clinic, setClinic] = useState<Clinic | null>(null);
  const [availableClinics, setAvailableClinics] = useState<Clinic[]>([]);
  const [selectedClinicId, setSelectedClinicId] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isSelectingClinic, setIsSelectingClinic] = useState(false);
  const [notification, setNotification] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error' | 'info' | 'warning'
  });

  useEffect(() => {
    const fetchClinic = async () => {
      if (!userData) return;

      // Get clinicId based on user role
      let clinicId;

      switch (userData.role) {
        case Role.Patient:
          clinicId = (userData as any).clinicId || (userData as any).asPatient?.clinicId;
          break;
        case Role.Client:
          clinicId = (userData as any).clinicId || (userData as any).asClient?.clinicId;
          break;
        case Role.Doctor:
          clinicId = (userData as any).clinicId || (userData as any).asDoctor?.clinicId;
          break;
        case Role.ClinicAdmin:
          clinicId = (userData as any).clinicId || (userData as any).asClinicAdmin?.clinicId;
          break;
        default:
          clinicId = null;
      }

      if (!clinicId) {
        setIsSelectingClinic(true);
        await fetchAvailableClinics();
        return;
      }

      setLoading(true);
      try {
        const clinicData = await getClinicById(clinicId);
        if (!clinicData) {
          setError('Assigned clinic not found.');
          setIsSelectingClinic(true);
          await fetchAvailableClinics();
        } else {
          setClinic(clinicData);
        }
      } catch {
        setError('Failed to load clinic data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchClinic();
  }, [userData]);

  const fetchAvailableClinics = async () => {
    setLoading(true);
    try {
      const clinicsData = await getClinics();
      setAvailableClinics(clinicsData);
      if (clinicsData.length === 0) {
        setError('No clinics available to select from.');
      }
    } catch {
      setError('Failed to load available clinics. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const handleClinicSelection = async (event: SelectChangeEvent<string>) => {
    const clinicId = event.target.value;
    setSelectedClinicId(clinicId);
  };

  const handleAssignClinic = async () => {
    if (!selectedClinicId || !userData?.uid) return;

    setLoading(true);
    try {
      // Update user based on role
      let updatedUserData: any = {};

      switch (userData.role) {
        case Role.Patient:
          updatedUserData = {
            asPatient: {
              ...(userData.asPatient || {}),
              clinicId: selectedClinicId
            }
          };
          break;
        case Role.Client:
          updatedUserData = {
            asClient: {
              ...(userData.asClient || {}),
              clinicId: selectedClinicId
            }
          };
          break;
        case Role.Doctor:
          updatedUserData = {
            asDoctor: {
              ...(userData.asDoctor || {}),
              clinicId: selectedClinicId
            }
          };
          break;
        case Role.ClinicAdmin:
          updatedUserData = {
            asClinicAdmin: {
              ...(userData.asClinicAdmin || {}),
              clinicId: selectedClinicId
            }
          };
          break;
        default:
          throw new Error('Invalid user role');
      }

      await updateUser(userData.uid, updatedUserData);

      // Fetch the selected clinic details
      const selectedClinic = await getClinicById(selectedClinicId);
      if (selectedClinic) {
        setClinic(selectedClinic);
        setIsSelectingClinic(false);
        setNotification({
          open: true,
          message: 'Clinic assigned successfully!',
          severity: 'success'
        });
      } else {
        throw new Error('Selected clinic not found');
      }
    } catch (error) {
      console.error('Error assigning clinic:', error);
      setNotification({
        open: true,
        message: 'Failed to assign clinic. Please try again.',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCloseNotification = () => {
    setNotification({
      ...notification,
      open: false
    });
  };

  const getHomeRoute = () => {
    if (!userData || !userData.role) return ROUTES.AUTH.LOGIN;

    switch (userData.role) {
      case Role.Patient:
        return ROUTES.HOMEPAGES.PATIENT;
      case Role.Client:
        return ROUTES.HOMEPAGES.CLIENT;
      case Role.Doctor:
        return ROUTES.HOMEPAGES.DOCTOR;
      case Role.ClinicAdmin:
        return ROUTES.HOMEPAGES.CLINIC_ADMIN;
      case Role.Admin:
        return ROUTES.HOMEPAGES.ADMIN;
      default:
        return ROUTES.AUTH.LOGIN;
    }
  };

  if (userLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '70vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (userError) {
    return (
      <Box sx={{ p: 3, display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', minHeight: '50vh' }}>
        <Card raised sx={{ maxWidth: 500, width: '100%', textAlign: 'center', p: 3 }}>
          <Typography variant="h4" gutterBottom>
            Error
          </Typography>
          <Typography variant="body1" color="error" paragraph>
            {userError.message || 'Failed to load user data.'}
          </Typography>
          <Button variant="contained" color="primary" startIcon={<ArrowBackIcon />} onClick={() => navigate(getHomeRoute())} sx={{ mt: 2 }}>
            Back to Home
          </Button>
        </Card>
      </Box>
    );
  }

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '70vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (isSelectingClinic) {
    return (
      <Box data-testid="select-clinic" sx={{ p: 3 }}>
        <Grid container spacing={3}>
          {/* Header */}
          <Grid item xs={12}>
            <Box display="flex" alignItems="center" mb={2}>
              <Button
                variant="outlined"
                color="primary"
                startIcon={<ArrowBackIcon />}
                onClick={() => navigate(getHomeRoute())}
                sx={{ mr: 2 }}
              >
                Back to Home
              </Button>
              <Typography variant="h3">Select Your Clinic</Typography>
            </Box>
          </Grid>

          {error && (
            <Grid item xs={12}>
              <Alert severity="error" sx={{ mb: 2 }}>
                {error}
              </Alert>
            </Grid>
          )}

          {/* Clinic selection form */}
          <Grid item xs={12}>
            <Card raised>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  You don't have a clinic assigned to your profile. Please select one from the list below:
                </Typography>

                {availableClinics.length === 0 ? (
                  <Alert severity="info">No clinics available to select. Please contact an administrator.</Alert>
                ) : (
                  <>
                    <FormControl fullWidth sx={{ my: 2 }}>
                      <InputLabel id="clinic-select-label">Select Clinic</InputLabel>
                      <Select
                        labelId="clinic-select-label"
                        id="clinic-select"
                        value={selectedClinicId}
                        label="Select Clinic"
                        onChange={handleClinicSelection}
                      >
                        {availableClinics.map((clinic) => (
                          <MenuItem key={clinic.id} value={clinic.id}>
                            {clinic.name}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>

                    <Button variant="contained" color="primary" onClick={handleAssignClinic} disabled={!selectedClinicId} sx={{ mt: 2 }}>
                      Assign to this Clinic
                    </Button>
                  </>
                )}
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        <Snackbar
          open={notification.open}
          autoHideDuration={6000}
          onClose={handleCloseNotification}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        >
          <Alert onClose={handleCloseNotification} severity={notification.severity} sx={{ width: '100%' }}>
            {notification.message}
          </Alert>
        </Snackbar>
      </Box>
    );
  }

  if (!clinic) {
    return null;
  }

  return (
    <Box data-testid="dashboard-clinic-admin" sx={{ p: 3 }}>
      <Grid container spacing={3}>
        {/* Header */}
        <Grid item xs={12}>
          <Box display="flex" alignItems="center" mb={3}>
            <Button
              variant="outlined"
              color="primary"
              startIcon={<ArrowBackIcon />}
              onClick={() => navigate(getHomeRoute())}
              sx={{ mr: 2 }}
            >
              Back to Home
            </Button>
            <Typography variant="h3">My Clinic</Typography>
          </Box>
        </Grid>

        {/* Clinic profile card */}
        <Grid item xs={12} md={8}>
          <Card raised sx={{ overflow: 'hidden' }}>
            <Box
              sx={{
                bgcolor: 'primary.main',
                py: 3,
                px: 3,
                display: 'flex',
                alignItems: 'center',
                color: 'white'
              }}
            >
              <Avatar
                sx={{
                  width: 80,
                  height: 80,
                  mr: 3,
                  bgcolor: 'white',
                  color: 'primary.main'
                }}
              >
                <BusinessIcon fontSize="large" />
              </Avatar>
              <Typography variant="h4">{clinic.name}</Typography>
            </Box>

            <CardContent sx={{ px: 3, py: 4 }}>
              <Grid container spacing={4}>
                {/* Contact Information */}
                <Grid item xs={12} md={6}>
                  <Box sx={{ mb: 3 }}>
                    <Typography
                      variant="h6"
                      gutterBottom
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        borderBottom: 1,
                        borderColor: 'divider',
                        pb: 1,
                        mb: 2
                      }}
                    >
                      <EmailIcon sx={{ mr: 1 }} /> Contact Information
                    </Typography>

                    <Box sx={{ ml: 1 }}>
                      <Typography variant="body1" sx={{ display: 'flex', alignItems: 'center', mb: 1.5 }}>
                        <EmailIcon color="primary" sx={{ mr: 2, fontSize: '1.2rem' }} />
                        <Box>
                          <Typography variant="subtitle2" color="text.secondary">
                            Email
                          </Typography>
                          <Typography>{clinic.contactEmail || 'N/A'}</Typography>
                        </Box>
                      </Typography>

                      <Typography variant="body1" sx={{ display: 'flex', alignItems: 'center' }}>
                        <PhoneIcon color="primary" sx={{ mr: 2, fontSize: '1.2rem' }} />
                        <Box>
                          <Typography variant="subtitle2" color="text.secondary">
                            Phone
                          </Typography>
                          <Typography>{clinic.contactPhone || 'N/A'}</Typography>
                        </Box>
                      </Typography>
                    </Box>
                  </Box>
                </Grid>

                {/* Location Information */}
                <Grid item xs={12} md={6}>
                  <Box>
                    <Typography
                      variant="h6"
                      gutterBottom
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        borderBottom: 1,
                        borderColor: 'divider',
                        pb: 1,
                        mb: 2
                      }}
                    >
                      <LocationOnIcon sx={{ mr: 1 }} /> Location
                    </Typography>

                    {clinic.address ? (
                      <Box sx={{ ml: 1 }}>
                        <Typography variant="body1" sx={{ display: 'flex', mb: 1 }}>
                          <LocationOnIcon color="primary" sx={{ mr: 2, fontSize: '1.2rem', alignSelf: 'flex-start', mt: 0.5 }} />
                          <Box>
                            <Typography variant="subtitle2" color="text.secondary">
                              Address
                            </Typography>
                            <Typography sx={{ mb: 0.5 }}>{clinic.address.street || 'N/A'}</Typography>
                            <Typography sx={{ mb: 0.5 }}>
                              {clinic.address.city ? `${clinic.address.city}, ` : ''}
                              {clinic.address.state || ''}
                              {clinic.address.zipCode ? ` ${clinic.address.zipCode}` : ''}
                            </Typography>
                            <Typography>{clinic.address.country || ''}</Typography>
                          </Box>
                        </Typography>
                      </Box>
                    ) : (
                      <Typography sx={{ ml: 5 }} color="text.secondary">
                        No address information available
                      </Typography>
                    )}
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Additional Information Card */}
        <Grid item xs={12} md={4}>
          <Card raised sx={{ height: '100%' }}>
            <CardContent sx={{ p: 3 }}>
              <Typography
                variant="h6"
                gutterBottom
                sx={{
                  borderBottom: 1,
                  borderColor: 'divider',
                  pb: 1,
                  mb: 2
                }}
              >
                Clinic Information
              </Typography>

              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Button variant="outlined" color="primary" startIcon={<BusinessIcon />} fullWidth>
                  View Clinic Details
                </Button>

                {clinic.description && (
                  <Box mt={2}>
                    <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                      About the Clinic
                    </Typography>
                    <Typography variant="body2">{clinic.description}</Typography>
                  </Box>
                )}

                {clinic.createdAt && (
                  <Box mt={1}>
                    <Typography variant="caption" color="text.secondary">
                      Member since: {clinic.createdAt.toDate().toLocaleDateString()}
                    </Typography>
                  </Box>
                )}
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={handleCloseNotification}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleCloseNotification} severity={notification.severity} sx={{ width: '100%' }}>
          {notification.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default MyClinic;
