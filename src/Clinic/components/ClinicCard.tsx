import { Card, CardContent, Typography, Box, Chip, Button, Stack, Avatar } from '@mui/material';
import BusinessIcon from '@mui/icons-material/Business';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import EmailIcon from '@mui/icons-material/Email';
import PhoneIcon from '@mui/icons-material/Phone';
import { Clinic } from '../[types]/Clinic';

interface ClinicCardProps {
  clinic: Clinic;
  onView: (clinicId: string) => void;
  onEdit?: (clinicId: string) => void;
  canEdit?: boolean;
}

const ClinicCard = ({ clinic, onView, onEdit, canEdit }: ClinicCardProps) => {
  return (
    <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'space-between', p: 2 }}>
      <CardContent>
        <Stack spacing={1} alignItems="center">
          <Avatar sx={{ bgcolor: 'primary.main', width: 56, height: 56 }}>
            <BusinessIcon fontSize="large" />
          </Avatar>
          <Typography variant="h6" fontWeight={600} noWrap gutterBottom>
            {clinic.name}
          </Typography>
          {clinic.address && (
            <Box display="flex" alignItems="center" gap={0.5} sx={{ width: '100%', justifyContent: 'center' }}>
              <LocationOnIcon fontSize="small" color="action" />
              <Typography variant="body2" color="text.secondary" noWrap>
                {clinic.address.city}, {clinic.address.state}
              </Typography>
            </Box>
          )}
          <Stack direction="row" spacing={1} justifyContent="center" sx={{ mt: 1 }}>
            {clinic.contactEmail && <Chip icon={<EmailIcon />} label={clinic.contactEmail} size="small" variant="outlined" />}
            {clinic.contactPhone && <Chip icon={<PhoneIcon />} label={clinic.contactPhone} size="small" variant="outlined" />}
          </Stack>
        </Stack>
      </CardContent>
      <Stack direction="row" spacing={1} justifyContent="center" pb={2}>
        <Button variant="outlined" color="primary" size="small" onClick={() => onView(clinic.id)}>
          View
        </Button>
        {canEdit && onEdit && (
          <Button variant="outlined" color="secondary" size="small" onClick={() => onEdit(clinic.id)}>
            Edit
          </Button>
        )}
      </Stack>
    </Card>
  );
};

export default ClinicCard;
