import { styled, useTheme } from '@mui/material/styles';
import { Card, Box, Typography, CardMedia } from '@mui/material';
import { Clinic } from '../[types]/Clinic';

interface ClinicStatCardProps {
  clinic: Clinic;
  onClick?: (clinicId: string) => void;
}

const CardWrapper = styled(Card)(({ theme }) => ({
  backgroundColor: theme.palette.background.paper,
  color: theme.palette.primary.light,
  overflow: 'hidden',
  borderRadius: 16,
  minHeight: 340,
  boxShadow: theme.shadows[5],
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'flex-start',
  cursor: 'pointer',
  transition: 'box-shadow 0.2s',
  '&:hover': {
    boxShadow: theme.shadows[10]
  }
}));

const defaultImage = new URL('../../assets/images/cards/card-1.jpg', import.meta.url).href;

export default function ClinicStatCard({ clinic, onClick }: ClinicStatCardProps) {
  const theme = useTheme();
  return (
    <CardWrapper onClick={() => onClick && onClick(clinic.id)}>
      <CardMedia
        component="img"
        image={defaultImage}
        alt={clinic.name}
        height="140"
        sx={{ borderRadius: '16px 16px 0 0', objectFit: 'cover', width: '100%' }}
      />
      <Box sx={{ p: 3, pb: 2, flexGrow: 1, bgcolor: 'primary.900' }}>
        <Typography variant="h6" fontWeight={700} color="#fff" gutterBottom noWrap>
          {clinic.name}
        </Typography>
        <Typography variant="body2" color="primary.200" sx={{ mb: 2, minHeight: 40 }} noWrap>
          {clinic.address?.street && `${clinic.address.street}, `}
          {clinic.address?.city}, {clinic.address?.state} {clinic.address?.zipCode}, {clinic.address?.country}
        </Typography>
        <Typography variant="caption" color="primary.300" sx={{ mt: 3, display: 'block' }}>
          Last updated {clinic.updatedAt?.toDate?.().toLocaleDateString?.() || clinic.createdAt?.toDate?.().toLocaleDateString?.() || '-'}
        </Typography>
      </Box>
    </CardWrapper>
  );
}
