// Clinic service
// Handles operations for clinics

import { db as firestore } from '../../Firebase/[config]/firebase';
import {
  collection,
  doc,
  getDoc,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  getDocs,
  arrayUnion,
  arrayRemove,
  orderBy,
  limit
} from 'firebase/firestore';
import { Clinic } from '../[types]/Clinic';
import { UserID } from '../../Users/<USER>/User';
import { Timestamp, DocumentData, QueryDocumentSnapshot } from 'firebase/firestore';

// Collection references
const clinicsCollection = collection(firestore, 'clinics');

/**
 * Convert Firestore document to Clinic
 */
export const convertToClinic = (doc: QueryDocumentSnapshot<DocumentData>): Clinic => {
  const data = doc.data();

  return {
    id: doc.id,
    name: data.name || '',
    address: data.address
      ? {
          street: data.address.street || '',
          state: data.address.state || '',
          zipCode: data.address.zipCode || '',
          country: data.address.country || '',
          city: data.address.city || ''
        }
      : undefined,
    contactEmail: data.contactEmail || '',
    contactPhone: data.contactPhone || '',
    administratorIds: data.administratorIds || [],
    createdAt: data.createdAt || Timestamp.now(),
    updatedAt: data.updatedAt || Timestamp.now()
  };
};

/**
 * Get a clinic by ID
 */
export const getClinicById = async (id: string): Promise<Clinic | null> => {
  try {
    const clinicRef = doc(clinicsCollection, id);
    const clinicSnap = await getDoc(clinicRef);
    if (!clinicSnap.exists()) return null;
    return convertToClinic(clinicSnap);
  } catch (error) {
    console.error('Error getting clinic by ID:', error);
    throw error;
  }
};

/**
 * Get all clinics with optional filtering
 */
export const getClinics = async (
  options: {
    isActive?: boolean;
    adminId?: UserID;
    doctorId?: UserID;
    limit?: number;
    orderByField?: string;
    orderDirection?: 'asc' | 'desc';
  } = {}
): Promise<Clinic[]> => {
  try {
    let q = query(clinicsCollection);

    // Apply filters
    if (options.isActive !== undefined) {
      q = query(q, where('isActive', '==', options.isActive));
    }

    if (options.adminId) {
      q = query(q, where('administratorIds', 'array-contains', options.adminId));
    }

    if (options.doctorId) {
      q = query(q, where('doctorIds', 'array-contains', options.doctorId));
    }

    // Apply sorting
    if (options.orderByField) {
      q = query(q, orderBy(options.orderByField, options.orderDirection || 'asc'));
    } else {
      q = query(q, orderBy('name', 'asc'));
    }

    // Apply limit
    if (options.limit) {
      q = query(q, limit(options.limit));
    }

    const querySnap = await getDocs(q);
    return querySnap.docs.map((doc: QueryDocumentSnapshot<DocumentData>) => convertToClinic(doc));
  } catch (error) {
    console.error('Error getting clinics:', error);
    throw error;
  }
};

/**
 * Create a new clinic
 */
export const createClinic = async (data: Omit<Clinic, 'id'>): Promise<Clinic | null> => {
  try {
    const now = Timestamp.now();
    const clinicData = {
      ...data,
      createdAt: now,
      updatedAt: now
    };

    const docRef = await addDoc(clinicsCollection, clinicData);
    const newClinic = await getDoc(docRef);
    if (!newClinic.exists()) return null;
    return convertToClinic(newClinic as QueryDocumentSnapshot<DocumentData>);
  } catch (error) {
    console.error('Error creating clinic:', error);
    throw error;
  }
};

/**
 * Update an existing clinic
 */
export const updateClinic = async (id: string, data: Partial<Clinic>): Promise<Clinic | null> => {
  try {
    const clinicRef = doc(clinicsCollection, id);
    const clinicDoc = await getDoc(clinicRef);

    if (!clinicDoc.exists()) {
      throw new Error(`Clinic with ID ${id} not found`);
    }

    const now = Timestamp.now();
    const dataWithoutId = { ...data };
    delete dataWithoutId.id;

    await updateDoc(clinicRef, {
      ...dataWithoutId,
      updatedAt: now
    });

    const updatedClinic = await getDoc(clinicRef);
    if (!updatedClinic.exists()) return null;
    return convertToClinic(updatedClinic as QueryDocumentSnapshot<DocumentData>);
  } catch (error) {
    console.error('Error updating clinic:', error);
    throw error;
  }
};

/**
 * Delete a clinic
 */
export const deleteClinic = async (id: string): Promise<void> => {
  try {
    await deleteDoc(doc(clinicsCollection, id));
  } catch (error) {
    console.error('Error deleting clinic:', error);
    throw error;
  }
};

/**
 * Add admin to clinic
 */
export const addAdminToClinic = async (clinicId: string, adminId: UserID): Promise<void> => {
  try {
    await updateDoc(doc(clinicsCollection, clinicId), {
      administratorIds: arrayUnion(adminId),
      updatedAt: Timestamp.now()
    });
  } catch (error) {
    console.error('Error adding admin to clinic:', error);
    throw error;
  }
};

/**
 * Remove admin from clinic
 */
export const removeAdminFromClinic = async (clinicId: string, adminId: UserID): Promise<void> => {
  try {
    await updateDoc(doc(clinicsCollection, clinicId), {
      administratorIds: arrayRemove(adminId),
      updatedAt: Timestamp.now()
    });
  } catch (error) {
    console.error('Error removing admin from clinic:', error);
    throw error;
  }
};

/**
 * Add doctor to clinic
 */
export const addDoctorToClinic = async (clinicId: string, doctorId: UserID): Promise<void> => {
  try {
    await updateDoc(doc(clinicsCollection, clinicId), {
      doctorIds: arrayUnion(doctorId),
      updatedAt: Timestamp.now()
    });
  } catch (error) {
    console.error('Error adding doctor to clinic:', error);
    throw error;
  }
};

/**
 * Remove doctor from clinic
 */
export const removeDoctorFromClinic = async (clinicId: string, doctorId: UserID): Promise<void> => {
  try {
    await updateDoc(doc(clinicsCollection, clinicId), {
      doctorIds: arrayRemove(doctorId),
      updatedAt: Timestamp.now()
    });
  } catch (error) {
    console.error('Error removing doctor from clinic:', error);
    throw error;
  }
};

/**
 * Get clinics created by month for time series (for dashboard sparklines)
 */
export const getClinicsByMonth = async (): Promise<{ month: string; count: number }[]> => {
  try {
    const now = new Date();
    const year = now.getFullYear();
    const startOfYear = new Date(year, 0, 1);
    const endOfYear = new Date(year, 11, 31, 23, 59, 59);
    let q = query(
      clinicsCollection,
      where('createdAt', '>=', Timestamp.fromDate(startOfYear)),
      where('createdAt', '<=', Timestamp.fromDate(endOfYear))
    );
    const querySnap = await getDocs(q);
    // Initialize counts for all 12 months
    const counts: { [month: number]: number } = {};
    for (let i = 0; i < 12; i++) counts[i] = 0;
    querySnap.docs.forEach((doc: QueryDocumentSnapshot<DocumentData>) => {
      const data = doc.data();
      const createdAt = data.createdAt?.toDate?.() || new Date();
      const month = createdAt.getMonth();
      counts[month]++;
    });
    // Convert to [{ month: 'Jan', count: N }, ...]
    return Object.keys(counts).map((m) => ({ month: getMonthAbbr(new Date(year, +m)), count: counts[+m] }));
  } catch (error) {
    console.error('Error getting clinics by month:', error);
    return [];
  }
};

function getMonthAbbr(date: Date): string {
  return date.toLocaleString('default', { month: 'short' });
}

import { getUserById } from '../../Users/<USER>/userService';

export async function getClinicsByUserId(userId: string): Promise<Clinic[]> {
  const user = await getUserById(userId);
  return getClinics({ doctorId: user?.asPatient?.assignedDoctorId });
}
