import Container from '@mui/material/Container';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import Stack from '@mui/material/Stack';
import Box from '@mui/material/Box';
import { useTheme } from '@mui/material/styles';

export default function FooterSection() {
  const theme = useTheme();
  const textColor = theme.palette.mode === 'dark' ? 'text.secondary' : 'text.hint';

  return (
    <>
      <Container sx={{ mb: 10 }}>
        <Grid container spacing={6}>
          <Grid item xs={12}>
            <Stack spacing={3} alignItems="center">
              <Typography variant="h4" color={textColor} sx={{ fontWeight: 600 }}>
                TitaniumRQ
              </Typography>
              <Typography variant="body2" color={textColor} align="center">
                TitaniumRQ is a modern platform for respiratory care, compliance, and patient engagement.
                <br />
                Empowering healthcare providers and patients with seamless, secure, and user-friendly solutions.
              </Typography>
            </Stack>
          </Grid>
        </Grid>
      </Container>
      <Box sx={{ bgcolor: 'dark.dark', py: { xs: 3, sm: 1.5 } }}>
        <Container>
          <Stack
            direction={{ xs: 'column', sm: 'row' }}
            alignItems="center"
            justifyContent="space-between"
            spacing={{ xs: 1.5, sm: 1, md: 3 }}
          >
            <Typography color="text.secondary" align="center">
              &copy; {new Date().getFullYear()} TitaniumRQ. All rights reserved.
            </Typography>
          </Stack>
        </Container>
      </Box>
    </>
  );
}
