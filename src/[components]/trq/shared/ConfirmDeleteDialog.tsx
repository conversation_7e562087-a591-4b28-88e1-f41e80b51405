import React from 'react';
import { useIntl } from 'react-intl';
import { Dialog, DialogTitle, DialogContent, DialogActions, Button, Typography, CircularProgress } from '@mui/material';

interface ConfirmDeleteDialogProps {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  contentText: string;
  isLoading?: boolean; // Optional loading state for the confirm button
}

const ConfirmDeleteDialog: React.FC<ConfirmDeleteDialogProps> = ({
  open,
  onClose,
  onConfirm,
  title,
  contentText,
  isLoading = false // Default to false
}) => {
  const intl = useIntl();

  return (
    <Dialog
      open={open}
      onClose={onClose}
      aria-labelledby="confirm-delete-dialog-title"
      aria-describedby="confirm-delete-dialog-description"
    >
      <DialogTitle id="confirm-delete-dialog-title">
        {title || intl.formatMessage({ id: 'confirm-delete' }) || 'Confirm Delete'}
      </DialogTitle>
      <DialogContent>
        <Typography id="confirm-delete-dialog-description">{contentText}</Typography>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} color="secondary">
          {intl.formatMessage({ id: 'cancel' }) || 'Cancel'}
        </Button>
        <Button onClick={onConfirm} color="error" variant="contained" autoFocus disabled={isLoading}>
          {isLoading ? <CircularProgress size={24} color="inherit" /> : intl.formatMessage({ id: 'delete' }) || 'Delete'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ConfirmDeleteDialog;
