// material-ui
import Grid from '@mui/material/Grid2';
import Typography from '@mui/material/Typography';

// project imports
import MainCard from './MainCard';
import { GenericCardProps } from '[types]/general';

// =============================|| ICON NUMBER CARD ||============================= //

interface IconNumberCardProps extends GenericCardProps {}

export default function IconNumberCard({ title, primary, secondary, iconPrimary, color }: IconNumberCardProps) {
  // Render the iconPrimary prop directly as it's already a ReactNode
  const primaryIcon = iconPrimary ? iconPrimary : null;

  return (
    <MainCard>
      <Grid container spacing={2} sx={{ alignItems: 'center' }}>
        <Grid size={12}>
          <Grid container sx={{ justifyContent: 'space-between', alignItems: 'center' }}>
            <Grid>
              <Typography variant="subtitle2" sx={{ color }}>
                {primaryIcon}
              </Typography>
              <Typography variant="h5" color="inherit">
                {title}
              </Typography>
            </Grid>
            <Grid>
              <Typography variant="h3">{primary}</Typography>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </MainCard>
  );
}
