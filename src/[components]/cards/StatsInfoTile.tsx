import React from 'react';
import { Paper, Box, Typography, useTheme } from '@mui/material';

// Define the props interface
export interface StatsInfoTileProps {
  /**
   * Title of the statistics tile
   */
  title: string;

  /**
   * Value to display in the tile
   */
  count: number;

  /**
   * Icon to display in the tile
   */
  icon: React.ReactNode;

  /**
   * Primary color for the tile (used for accents and icon background)
   */
  color?: string;

  /**
   * Tile variant - determines the visual style
   */
  variant?: 'gradient' | 'pattern' | 'simple';

  /**
   * Index used for pattern styles when multiple tiles are shown
   */
  index?: number;

  /**
   * Custom styling object
   */
  sx?: object;
}

/**
 * StatsInfoTile - A reusable component for displaying statistics in a visually appealing tile
 *
 * This component can be used across various user management sections to display key statistics
 * with consistent styling but customizable appearance.
 */
const StatsInfoTile: React.FC<StatsInfoTileProps> = ({ title, count, icon, color, variant = 'pattern', index = 0, sx = {} }) => {
  const theme = useTheme();

  // Predefined color patterns
  const colorSets = [
    { base: '3, 169, 244', name: 'blue' }, // Blue
    { base: '76, 175, 80', name: 'green' }, // Green
    { base: '33, 150, 243', name: 'lightblue' }, // Light Blue
    { base: '255, 152, 0', name: 'orange' } // Orange
  ];

  // Use provided color or fall back to the color set based on index
  const colorSet = colorSets[index % colorSets.length];
  const tileColor = color || `rgb(${colorSet.base})`;

  // Define different pattern styles for each card
  const patterns = [
    {
      bg: `linear-gradient(135deg, ${theme.palette.background.paper} 0%, rgba(${colorSet.base}, 0.12) 100%)`,
      bgHover: `linear-gradient(135deg, ${theme.palette.background.paper} 0%, rgba(${colorSet.base}, 0.2) 100%)`,
      pattern: `radial-gradient(circle, rgba(${colorSet.base}, 0.05) 8%, transparent 8%)`,
      patternSize: '20px 20px'
    },
    {
      // Simple gradient style
      bg: `linear-gradient(135deg, ${theme.palette.background.paper} 0%, rgba(${colorSet.base}, 0.08) 100%)`,
      bgHover: `linear-gradient(135deg, ${theme.palette.background.paper} 0%, rgba(${colorSet.base}, 0.15) 100%)`,
      pattern: 'none',
      patternSize: '0'
    },
    {
      // Solid background with slight opacity
      bg: theme.palette.mode === 'dark' ? `rgba(${colorSet.base}, 0.15)` : `rgba(${colorSet.base}, 0.08)`,
      bgHover: theme.palette.mode === 'dark' ? `rgba(${colorSet.base}, 0.25)` : `rgba(${colorSet.base}, 0.15)`,
      pattern: 'none',
      patternSize: '0'
    }
  ];

  // Select the pattern based on variant
  const patternStyle = variant === 'pattern' ? patterns[0] : variant === 'gradient' ? patterns[1] : patterns[2];

  return (
    <Paper
      elevation={3}
      sx={{
        p: 3,
        borderRadius: 2,
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        position: 'relative',
        overflow: 'hidden',
        transition: 'all 0.3s ease',
        background: patternStyle.bg,
        backgroundSize: '200% 200%',
        backgroundPosition: 'center',
        border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255,255,255,0.12)' : 'rgba(0,0,0,0.06)'}`,
        '&::before':
          patternStyle.pattern !== 'none'
            ? {
                content: '""',
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                backgroundImage: patternStyle.pattern,
                backgroundSize: patternStyle.patternSize,
                opacity: 0.5,
                zIndex: 0
              }
            : {},
        '&:hover': {
          transform: 'translateY(-5px)',
          boxShadow: theme.shadows[10],
          background: patternStyle.bgHover,
          border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255,255,255,0.2)' : 'rgba(0,0,0,0.1)'}`
        },
        ...sx
      }}
    >
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          right: 0,
          p: 1.5,
          color: 'white',
          bgcolor: color || tileColor,
          borderRadius: '0 4px 0 16px',
          zIndex: 2,
          boxShadow: '0 2px 10px rgba(0,0,0,0.2)'
        }}
      >
        {icon}
      </Box>
      <Box sx={{ position: 'relative', zIndex: 1, mt: 5 }}>
        <Typography variant="h3" fontWeight={700}>
          {typeof count === 'number' ? count.toLocaleString() : count}
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ mt: 1, fontWeight: 500 }}>
          {title}
        </Typography>
      </Box>
    </Paper>
  );
};

export default StatsInfoTile;
