import { useState, ChangeEvent, useEffect } from 'react';

// material-ui
import Button from '@mui/material/Button';
import FormControl from '@mui/material/FormControl';
import Radio from '@mui/material/Radio';
import FormControlLabel from '@mui/material/FormControlLabel';
import RadioGroup from '@mui/material/RadioGroup';

// third party
import { SnackbarProvider, useSnackbar } from 'notistack';

// project imports
import SubCard from '[components]/cards/SubCard';

// assets
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import SentimentSatisfiedAltIcon from '@mui/icons-material/SentimentSatisfiedAlt';

// ==============================|| NOTISTACK - CUSTOM ICON ||============================== //

interface IconVariantsContentProps {
  value: string;
  onChange: (value: string) => void;
}

function IconVariantsContent({ value, onChange }: IconVariantsContentProps) {
  const { enqueueSnackbar } = useSnackbar();

  const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
    const selectedValue = event.target.value;
    onChange(selectedValue);
  };

  const handleClick = () => {
    enqueueSnackbar('Your notification here', { variant: 'info' });
  };

  return (
    <SubCard title="With Icons">
      <FormControl>
        <RadioGroup
          row
          aria-labelledby="demo-row-radio-buttons-group-label"
          value={value}
          onChange={handleChange}
          name="row-radio-buttons-group"
        >
          <FormControlLabel value="usedefault" control={<Radio />} label="Use Default" />
          <FormControlLabel value="useemojis" control={<Radio />} label="Use Emojis" />
          <FormControlLabel value="hide" control={<Radio />} label="Hide" />
        </RadioGroup>
      </FormControl>
      <Button variant="contained" fullWidth sx={{ marginBlockStart: 2 }} onClick={handleClick}>
        Show Snackbar
      </Button>
    </SubCard>
  );
}

export default function IconVariants() {
  const infoIcon = <InfoOutlinedIcon sx={{ mr: 1 }} />;
  const emojiIcon = <SentimentSatisfiedAltIcon sx={{ mr: 1 }} />;
  const [icon, setIcon] = useState(infoIcon);
  const [hideIcon, setHideIcon] = useState(false);
  const [iconVariant, setIconVariant] = useState('usedefault');

  useEffect(() => {
    if (iconVariant === 'useemojis') {
      setIcon(emojiIcon);
      setHideIcon(false);
    } else if (iconVariant === 'usedefault') {
      setIcon(infoIcon);
      setHideIcon(false);
    } else if (iconVariant === 'hide') {
      setHideIcon(true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [iconVariant]);

  return (
    <SnackbarProvider iconVariant={hideIcon ? undefined : { info: icon }} hideIconVariant={hideIcon} maxSnack={3}>
      <IconVariantsContent value={iconVariant} onChange={setIconVariant} />
    </SnackbarProvider>
  );
}
