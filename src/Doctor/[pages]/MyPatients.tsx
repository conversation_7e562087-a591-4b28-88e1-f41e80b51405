import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from 'Authentication/[contexts]/AuthContext';
import { getUsers } from '../../Users/<USER>/userService';
import { TRQUser } from '../../Users/<USER>/User';
import { Role } from 'RBAC/[types]/Role';
import {
  Box,
  Grid,
  Typography,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Avatar,
  Button,
  CircularProgress,
  Divider,
  IconButton,
  TextField,
  InputAdornment
} from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import VisibilityIcon from '@mui/icons-material/Visibility';
import SearchIcon from '@mui/icons-material/Search';
import MessageIcon from '@mui/icons-material/Message';
import AssignmentIcon from '@mui/icons-material/Assignment';
import ROUTES from 'Routing/appRoutes';

/**
 * MyPatients Page
 * Shows the patients assigned to the currently logged-in doctor.
 */
const MyPatients = () => {
  const navigate = useNavigate();
  const { userData, isLoading: userLoading } = useAuth();

  const [patients, setPatients] = useState<TRQUser[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchText, setSearchText] = useState('');

  useEffect(() => {
    const fetchPatients = async () => {
      if (!userData) return;

      try {
        setLoading(true);

        // Verify the user is a doctor
        if (userData.role !== Role.Doctor) {
          setError('Only doctors can view their patients');
          setLoading(false);
          return;
        }

        // Get patients assigned to this doctor
        // Option 1: If patients are stored in the doctor's data
        if (userData.asDoctor?.patients && userData.asDoctor.patients.length > 0) {
          const doctorPatients = await getUsers({ patientUids: userData.asDoctor.patients });
          setPatients(doctorPatients);
        } else {
          // Option 2: Query patients with this doctor ID
          const doctorPatients = await getUsers({ doctorId: userData.uid });
          setPatients(doctorPatients);
        }

        setLoading(false);
      } catch (err) {
        console.error('Error fetching patients data:', err);
        setError('Failed to load patients data. Please try again later.');
        setLoading(false);
      }
    };

    if (userData) {
      fetchPatients();
    }
  }, [userData]);

  // Filter patients based on search text
  const filteredPatients = patients.filter(
    (patient) =>
      (patient.firstName?.toLowerCase() || '').includes(searchText.toLowerCase()) ||
      (patient.lastName?.toLowerCase() || '').includes(searchText.toLowerCase()) ||
      patient.email.toLowerCase().includes(searchText.toLowerCase())
  );

  if (userLoading || loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '70vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3, display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', minHeight: '50vh' }}>
        <Card raised sx={{ maxWidth: 500, width: '100%', textAlign: 'center', p: 3 }}>
          <Typography variant="h4" gutterBottom>
            My Patients
          </Typography>
          <Typography variant="body1" color="error" paragraph>
            {error}
          </Typography>
          <Button
            variant="contained"
            color="primary"
            startIcon={<ArrowBackIcon />}
            onClick={() => navigate(ROUTES.HOMEPAGES.DOCTOR)}
            sx={{ mt: 2 }}
          >
            Back to Home
          </Button>
        </Card>
      </Box>
    );
  }

  return (
    <Box data-testid="my-patients" sx={{ p: 3 }}>
      <Grid container spacing={3}>
        {/* Header */}
        <Grid item xs={12}>
          <Box display="flex" alignItems="center" mb={2}>
            <Button
              variant="outlined"
              color="primary"
              startIcon={<ArrowBackIcon />}
              onClick={() => navigate(ROUTES.HOMEPAGES.DOCTOR)}
              sx={{ mr: 2 }}
            >
              Back to Home
            </Button>
            <Typography variant="h3">My Patients</Typography>
          </Box>
        </Grid>

        {/* Search Box */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <TextField
                fullWidth
                variant="outlined"
                placeholder="Search patients by name or email..."
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  )
                }}
              />
            </CardContent>
          </Card>
        </Grid>

        {/* Patients List */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h4" gutterBottom>
                Patient List ({filteredPatients.length})
              </Typography>
              {filteredPatients.length === 0 ? (
                <Typography variant="body1" align="center" sx={{ py: 4 }}>
                  {searchText ? 'No patients match your search criteria.' : 'You have no patients assigned to you.'}
                </Typography>
              ) : (
                <List>
                  {filteredPatients.map((patient) => (
                    <React.Fragment key={patient.uid}>
                      <ListItem
                        secondaryAction={
                          <Box>
                            <IconButton
                              color="primary"
                              title="View Patient Details"
                              onClick={() => navigate(`/trq/patients/${patient.uid}`)}
                            >
                              <VisibilityIcon />
                            </IconButton>
                            <IconButton color="secondary" title="Assign Questionnaire">
                              <AssignmentIcon />
                            </IconButton>
                            <IconButton color="default" title="Message Patient">
                              <MessageIcon />
                            </IconButton>
                          </Box>
                        }
                      >
                        <ListItemAvatar>
                          <Avatar>
                            {((patient.firstName?.[0] || '') + (patient.lastName?.[0] || '') || patient.email[0]).toUpperCase()}
                          </Avatar>
                        </ListItemAvatar>
                        <ListItemText
                          primary={`${patient.firstName || ''} ${patient.lastName || ''}`.trim() || patient.email}
                          secondary={
                            <>
                              <Typography component="span" variant="body2" color="text.primary">
                                {patient.email}
                              </Typography>
                              <br />
                              {patient.asPatient?.dateOfBirth
                                ? `Date of Birth: ${new Date(patient.asPatient.dateOfBirth.toString()).toLocaleDateString()}`
                                : 'No date of birth available'}
                            </>
                          }
                        />
                      </ListItem>
                      <Divider variant="inset" component="li" />
                    </React.Fragment>
                  ))}
                </List>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default MyPatients;
