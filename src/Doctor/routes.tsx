import { lazy } from 'react';
import Loadable from '[components]/Loadable';
import PermissionRoute from 'RBAC/PermissionRoute';
import { PERMISSIONS } from 'RBAC/permissions';
import ROUTES from 'Routing/appRoutes';

// Doctor pages
const DoctorDashboard = Loadable(lazy(() => import('./[pages]/DoctorDashboard')));
const DoctorDetails = Loadable(lazy(() => import('./[pages]/DoctorDetails')));
const DoctorProfile = Loadable(lazy(() => import('./[pages]/DoctorProfile')));
const AllDoctors = Loadable(lazy(() => import('./[pages]/AllDoctors')));
const AddDoctor = Loadable(lazy(() => import('./[pages]/AddDoctor')));
const EditDoctor = Loadable(lazy(() => import('./[pages]/EditDoctor')));
const MyDoctor = Loadable(lazy(() => import('./[pages]/MyDoctor')));

// Routes for Doctor module
const doctorRoutes = {
  path: 'doctors', // Updated base path from 'Doctor' to 'doctors'
  children: [
    {
      path: '',
      element: (
        <PermissionRoute resource={PERMISSIONS.VIEW_USERS.resource} permissionAction={PERMISSIONS.LIST_USERS.action}>
          <AllDoctors />
        </PermissionRoute>
      )
    },
    {
      path: ROUTES.DOCTORS.DETAILS_PATTERN,
      element: (
        <PermissionRoute resource={PERMISSIONS.VIEW_USERS.resource} permissionAction={PERMISSIONS.VIEW_USERS.action}>
          <DoctorDetails />
        </PermissionRoute>
      )
    },
    {
      path: 'Home', // Updated path to match expected URL segment
      element: (
        <PermissionRoute
          resource={PERMISSIONS.VIEW_PATIENT.resource}
          permissionAction={PERMISSIONS.VIEW_PATIENT.action}
          redirectTo="/trq/unauthorized"
        >
          <DoctorDashboard />
        </PermissionRoute>
      )
    },
    {
      path: ROUTES.DOCTORS.PROFILE_PATTERN,
      element: (
        <PermissionRoute resource={PERMISSIONS.VIEW_USERS.resource} permissionAction={PERMISSIONS.VIEW_USERS.action}>
          <DoctorProfile />
        </PermissionRoute>
      )
    },
    {
      path: ROUTES.DOCTORS.ADD_PATTERN,
      element: (
        <PermissionRoute resource={PERMISSIONS.CREATE_USER.resource} permissionAction={PERMISSIONS.CREATE_USER.action}>
          <AddDoctor />
        </PermissionRoute>
      )
    },
    {
      path: ROUTES.DOCTORS.EDIT_PATTERN,
      element: (
        <PermissionRoute resource={PERMISSIONS.UPDATE_USER.resource} permissionAction={PERMISSIONS.UPDATE_USER.action}>
          <EditDoctor />
        </PermissionRoute>
      )
    },
    {
      path: 'my-doctor',
      element: <MyDoctor />
    }
  ]
};

export default doctorRoutes;
