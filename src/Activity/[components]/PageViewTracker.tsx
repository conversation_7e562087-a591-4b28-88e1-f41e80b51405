import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { useActivityPageTracking } from 'Activity/[contexts]/ActivityContext';

/**
 * Component that tracks page views automatically when the route changes.
 * This must be used within a Router context.
 */
const PageViewTracker: React.FC = () => {
  const location = useLocation();
  const trackPageView = useActivityPageTracking();

  useEffect(() => {
    const pathname = location.pathname;
    const pageTitle = document.title || pathname;

    // Track the page view
    trackPageView(pathname, pageTitle);
  }, [location, trackPageView]);

  // This component doesn't render anything
  return null;
};

export default PageViewTracker;
