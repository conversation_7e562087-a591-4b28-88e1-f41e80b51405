import React from 'react';
import { FormControl, FormHelperText, Slider, Typography, Box } from '@mui/material';
import { Controller } from 'react-hook-form';

interface ScaleQuestionProps {
  name: string;
  control: any;
  label: string;
  required?: boolean;
  error?: boolean;
  helperText?: string;
  disabled?: boolean;
  defaultValue?: number;
  min?: number;
  max?: number;
  step?: number;
  minLabel?: string;
  maxLabel?: string;
}

const ScaleQuestion: React.FC<ScaleQuestionProps> = ({
  name,
  control,
  label,
  required = false,
  error = false,
  helperText,
  disabled = false,
  defaultValue = 0,
  min = 0,
  max = 10,
  step = 1,
  minLabel = 'Not at all',
  maxLabel = 'Very much'
}) => {
  return (
    <FormControl fullWidth error={error} disabled={disabled}>
      <Typography gutterBottom>{label}</Typography>
      <Controller
        name={name}
        control={control}
        defaultValue={defaultValue}
        rules={{
          required: required ? 'This field is required' : false
        }}
        render={({ field }) => (
          <Box sx={{ px: 2 }}>
            <Slider
              {...field}
              value={field.value || defaultValue}
              onChange={(_, value) => field.onChange(value)}
              min={min}
              max={max}
              step={step}
              marks
              valueLabelDisplay="auto"
              disabled={disabled}
            />
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
              <Typography variant="caption" color="text.secondary">
                {minLabel}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {maxLabel}
              </Typography>
            </Box>
          </Box>
        )}
      />
      {helperText && <FormHelperText>{helperText}</FormHelperText>}
    </FormControl>
  );
};

export default ScaleQuestion;
