import { styled, useTheme } from '@mui/material/styles';
import Avatar from '@mui/material/Avatar';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import MainCard from '[components]/cards/MainCard';
import InsertDriveFileOutlinedIcon from '@mui/icons-material/InsertDriveFileOutlined';
import CategoryIcon from '@mui/icons-material/Category';
import Chip from '@mui/material/Chip';

interface TemplateStatCardProps {
  title: string;
  value: string | number;
  icon?: React.ReactNode;
  subtext?: string;
  color?: string;
  up?: boolean;
  category?: string;
  coverImage?: string;
  isPublished?: boolean;
}

// --- SVG Background Variants (Subtle) ---
const svgBackgrounds = [
  // Abstract Blob (subtle opacity)
  (theme: any) =>
    `url('data:image/svg+xml;utf8,<svg width="320" height="180" viewBox="0 0 320 180" fill="none" xmlns="http://www.w3.org/2000/svg"><ellipse cx="160" cy="90" rx="160" ry="90" fill="${encodeURIComponent(theme.palette.primary[100])}" fill-opacity="0.18"/><ellipse cx="220" cy="60" rx="80" ry="40" fill="${encodeURIComponent(theme.palette.secondary.light)}" fill-opacity="0.11"/></svg>')`,
  // Wavy Gradient (subtle opacity)
  (theme: any) =>
    `url('data:image/svg+xml;utf8,<svg width="400" height="200" viewBox="0 0 400 200" fill="none" xmlns="http://www.w3.org/2000/svg"><defs><linearGradient id="cardWave" x1="0" y1="0" x2="400" y2="200" gradientUnits="userSpaceOnUse"><stop stop-color="${encodeURIComponent(theme.palette.primary[50])}"/><stop offset="1" stop-color="${encodeURIComponent(theme.palette.secondary.light)}"/></linearGradient></defs><path d="M0 100 Q100 150 200 100 T400 100 V200 H0 Z" fill="url(%23cardWave)" fill-opacity="0.10"/></svg>')`,
  // Geometric Pattern (subtle opacity)
  (theme: any) =>
    `url('data:image/svg+xml;utf8,<svg width="200" height="200" fill="none" xmlns="http://www.w3.org/2000/svg"><rect x="0" y="0" width="200" height="200" fill="${encodeURIComponent(theme.palette.primary[50])}" fill-opacity="0.11"/><circle cx="50" cy="50" r="30" fill="${encodeURIComponent(theme.palette.secondary.light)}" fill-opacity="0.09"/><rect x="120" y="120" width="60" height="60" rx="15" fill="${encodeURIComponent(theme.palette.secondary.light)}" fill-opacity="0.07"/></svg>')`,
  // Subtle Dots (subtle opacity)
  (theme: any) =>
    `url('data:image/svg+xml;utf8,<svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="10" cy="10" r="2" fill="${encodeURIComponent(theme.palette.primary[200])}" fill-opacity="0.11"/><circle cx="30" cy="30" r="2" fill="${encodeURIComponent(theme.palette.secondary.main)}" fill-opacity="0.09"/></svg>')`
];

// --- Updated CardWrapper with dynamic SVG background ---
const CardWrapper = styled(MainCard)<{ bgVariant?: number; hasCoverImage?: boolean }>(
  ({ theme, bgVariant = 0, hasCoverImage = false }) => ({
    backgroundColor: theme.palette.primary.dark,
    color: theme.palette.primary.light,
    overflow: 'hidden',
    position: 'relative',
    minHeight: 120,
    backgroundImage: !hasCoverImage ? svgBackgrounds[bgVariant % svgBackgrounds.length](theme) : 'none',
    backgroundRepeat: 'no-repeat',
    backgroundSize: 'cover',
    '&:after': {
      content: '""',
      position: 'absolute',
      width: 180,
      height: 180,
      background: `linear-gradient(210.04deg, ${theme.palette.primary[200]} -50.94%, rgba(144, 202, 249, 0) 83.49%)`,
      borderRadius: '50%',
      top: -30,
      right: -120,
      opacity: hasCoverImage ? 0 : 0.09
    },
    '&:before': {
      content: '""',
      position: 'absolute',
      width: 180,
      height: 180,
      background: `linear-gradient(140.9deg, ${theme.palette.primary[200]} -14.02%, rgba(144, 202, 249, 0) 77.58%)`,
      borderRadius: '50%',
      top: -120,
      right: -80,
      opacity: hasCoverImage ? 0 : 0.07
    }
  })
);

// Cover image style with overlay
const CoverImageWrapper = styled(Box)({
  position: 'absolute',
  top: 0,
  left: 0,
  width: '100%',
  height: '100%',
  overflow: 'hidden',
  zIndex: 0,
  '&::after': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: 'rgba(0, 0, 0, 0.6)',
    zIndex: 1
  }
});

const CoverImage = styled('img')({
  width: '100%',
  height: '100%',
  objectFit: 'cover',
  display: 'block'
});

export default function TemplateStatCard({
  title,
  value,
  icon,
  subtext,
  color,
  up,
  bgVariant = 0, // new prop for background variant
  category,
  coverImage,
  isPublished
}: TemplateStatCardProps & { bgVariant?: number }) {
  const theme = useTheme();
  const hasCoverImage = !!coverImage;

  // Format the subtext to consistently show the published status
  const formattedSubtext = () => {
    let statusText = '';

    // Extract version if it exists in the subtext
    const versionMatch = subtext?.match(/v\d+/) || [''];
    const versionText = versionMatch[0];

    // Add the draft/published status
    if (isPublished !== undefined) {
      statusText = isPublished ? 'Published' : 'Draft';
    }

    // Combine version and status
    if (versionText && statusText) {
      return `${versionText} | ${statusText}`;
    } else if (versionText) {
      return versionText;
    } else if (statusText) {
      return statusText;
    }

    // Fall back to original subtext if we couldn't extract meaningful information
    return subtext || '';
  };

  return (
    <CardWrapper border={false} content={false} bgVariant={bgVariant} hasCoverImage={hasCoverImage}>
      {hasCoverImage && (
        <CoverImageWrapper>
          <CoverImage src={coverImage} alt={title} />
        </CoverImageWrapper>
      )}
      <Box
        sx={{
          p: 3,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          minHeight: 180,
          justifyContent: 'center',
          textAlign: 'center',
          position: 'relative',
          zIndex: 2
        }}
      >
        <Avatar
          variant="rounded"
          sx={{
            bgcolor: color || theme.palette.primary[700],
            color: '#fff',
            width: 56,
            height: 56,
            mb: 2,
            boxShadow: 2,
            fontSize: 32,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
        >
          {icon || <InsertDriveFileOutlinedIcon fontSize="large" />}
        </Avatar>
        <Typography variant="h3" sx={{ color: '#fff', fontWeight: 700, mb: 0.5, lineHeight: 1.2 }}>
          {value}
          {up && (
            <Box
              component="span"
              sx={{
                ml: 1,
                color: theme.palette.success.light,
                fontSize: 18,
                verticalAlign: 'middle',
                fontWeight: 700
              }}
            >
              ↑
            </Box>
          )}
        </Typography>
        <Typography variant="subtitle1" sx={{ color: 'primary.light', fontWeight: 500, mb: 0.5 }}>
          {title}
        </Typography>
        {category && (
          <Chip
            icon={<CategoryIcon />}
            label={category}
            size="small"
            sx={{ mt: 0.5, mb: 0.5, bgcolor: 'background.paper', color: 'text.primary', fontWeight: 500 }}
            variant="outlined"
          />
        )}
        {(subtext || isPublished !== undefined) && (
          <Typography variant="caption" sx={{ color: 'text.secondary', opacity: 0.72 }}>
            {formattedSubtext()}
          </Typography>
        )}
      </Box>
    </CardWrapper>
  );
}
