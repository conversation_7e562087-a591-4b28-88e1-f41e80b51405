import React from 'react';
import { FormControl, FormHelperText } from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { Controller } from 'react-hook-form';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';

interface DateQuestionProps {
  name: string;
  control: any;
  label: string;
  required?: boolean;
  error?: boolean;
  helperText?: string;
  disabled?: boolean;
  defaultValue?: Date | null;
}

const DateQuestion: React.FC<DateQuestionProps> = ({
  name,
  control,
  label,
  required = false,
  error = false,
  helperText,
  disabled = false,
  defaultValue = null
}) => {
  return (
    <FormControl fullWidth error={error} disabled={disabled}>
      <LocalizationProvider dateAdapter={AdapterDateFns}>
        <Controller
          name={name}
          control={control}
          defaultValue={defaultValue}
          rules={{
            required: required ? 'This field is required' : false
          }}
          render={({ field: { onChange, value, ...field } }) => (
            <DatePicker
              {...field}
              label={label}
              value={value}
              onChange={onChange}
              disabled={disabled}
              slotProps={{
                textField: {
                  fullWidth: true,
                  required,
                  error,
                  helperText
                }
              }}
            />
          )}
        />
      </LocalizationProvider>
      {helperText && <FormHelperText>{helperText}</FormHelperText>}
    </FormControl>
  );
};

export default DateQuestion;
