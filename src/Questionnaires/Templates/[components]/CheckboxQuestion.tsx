import React from 'react';
import { FormControl, FormGroup, FormControlLabel, Checkbox, FormHelperText, FormLabel } from '@mui/material';
import { Controller } from 'react-hook-form';

interface CheckboxQuestionProps {
  name: string;
  control: any;
  label: string;
  required?: boolean;
  error?: boolean;
  helperText?: string;
  disabled?: boolean;
  defaultValue?: string[];
  options?: { value: string; label: string }[];
}

const CheckboxQuestion: React.FC<CheckboxQuestionProps> = ({
  name,
  control,
  label,
  required = false,
  error = false,
  helperText,
  disabled = false,
  defaultValue = [],
  options = []
}) => {
  return (
    <FormControl component="fieldset" error={error} disabled={disabled}>
      <FormLabel component="legend">{label}</FormLabel>
      <Controller
        name={name}
        control={control}
        defaultValue={defaultValue}
        rules={{
          required: required ? 'This field is required' : false,
          validate: (value) => {
            if (required && (!value || value.length === 0)) {
              return 'At least one option must be selected';
            }
            return true;
          }
        }}
        render={({ field }) => (
          <FormGroup>
            {options.map((option) => (
              <FormControlLabel
                key={option.value}
                control={
                  <Checkbox
                    checked={field.value?.includes(option.value) || false}
                    onChange={(e) => {
                      const newValue = e.target.checked
                        ? [...(field.value || []), option.value]
                        : field.value?.filter((v: string) => v !== option.value) || [];
                      field.onChange(newValue);
                    }}
                    disabled={disabled}
                  />
                }
                label={option.label}
              />
            ))}
          </FormGroup>
        )}
      />
      {helperText && <FormHelperText>{helperText}</FormHelperText>}
    </FormControl>
  );
};

export default CheckboxQuestion;
