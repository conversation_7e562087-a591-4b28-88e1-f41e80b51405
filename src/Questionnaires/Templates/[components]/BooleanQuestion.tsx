import React from 'react';
import { FormControl, FormControlLabel, FormHelperText, Radio, RadioGroup } from '@mui/material';
import { Controller } from 'react-hook-form';

interface BooleanQuestionProps {
  name: string;
  control: any;
  label: string;
  required?: boolean;
  error?: boolean;
  helperText?: string;
  disabled?: boolean;
  defaultValue?: boolean;
}

const BooleanQuestion: React.FC<BooleanQuestionProps> = ({
  name,
  control,
  label,
  required = false,
  error = false,
  helperText,
  disabled = false,
  defaultValue = false
}) => {
  return (
    <FormControl component="fieldset" error={error} disabled={disabled}>
      <Controller
        name={name}
        control={control}
        defaultValue={defaultValue}
        rules={{
          required: required ? 'This field is required' : false
        }}
        render={({ field }) => (
          <RadioGroup
            {...field}
            row
            aria-label={label}
            name={name}
            value={field.value ? 'true' : 'false'}
            onChange={(e) => field.onChange(e.target.value === 'true')}
          >
            <FormControlLabel value="true" control={<Radio />} label="Yes" disabled={disabled} />
            <FormControlLabel value="false" control={<Radio />} label="No" disabled={disabled} />
          </RadioGroup>
        )}
      />
      {helperText && <FormHelperText>{helperText}</FormHelperText>}
    </FormControl>
  );
};

export default BooleanQuestion;
