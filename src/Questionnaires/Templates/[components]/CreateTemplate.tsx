import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  Box,
  Button,
  TextField,
  Typography,
  Grid,
  Paper,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Skeleton,
  CircularProgress,
  Card,
  CardMedia,
  Divider,
  Alert
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  ArrowUpward as MoveUpIcon,
  ArrowDownward as MoveDownIcon,
  Save as SaveIcon,
  PhotoCamera as PhotoCameraIcon,
  CloudUpload as CloudUploadIcon,
  AutoAwesome as AutoAwesomeIcon,
  Psychology as PsychologyIcon
} from '@mui/icons-material';
import { useForm, useFieldArray, Controller } from 'react-hook-form';
import { Question } from 'Questionnaires/[types]/Question';
// project imports
import { getTemplateById, createTemplate, updateTemplate } from '../[services]/questionnaireTemplateService';
import ImageUploader from 'ImageUploader/ImageUploader';

import MainCard from '[components]/cards/MainCard';
import { QuestionType } from 'Questionnaires/[types]/QuestionType';
import { QuestionnaireTemplate } from 'Questionnaires/[types]/QuestionnaireTemplate';

const questionTypes = [
  { value: QuestionType.TEXT, label: 'Text' },
  { value: QuestionType.BOOLEAN, label: 'Yes/No' },
  { value: QuestionType.CHECKBOX, label: 'Checkbox' },
  { value: QuestionType.SCALE, label: 'Scale' },
  { value: QuestionType.DATE, label: 'Date' },
  { value: QuestionType.SELECT, label: 'Select' }
];

interface TemplateFormData extends Omit<QuestionnaireTemplate, 'questions'> {
  questions: Array<Question & { optionsString?: string }>;
}

const CreateTemplate = () => {
  const { templateId } = useParams<{ templateId: string }>();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [questionToDelete, setQuestionToDelete] = useState<number | null>(null);
  const [coverImageUrl, setCoverImageUrl] = useState<string | null>(null);
  const [imageUploadModalOpen, setImageUploadModalOpen] = useState(false);

  // AI Generation states
  const [aiGenerationModalOpen, setAiGenerationModalOpen] = useState(false);
  const [aiDescription, setAiDescription] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationError, setGenerationError] = useState<string | null>(null);

  const {
    control,
    handleSubmit,
    reset,
    watch,
    setValue,
    formState: { errors }
  } = useForm<TemplateFormData>({
    defaultValues: {
      name: '',
      title: '',
      description: '',
      isPublished: false,
      version: 1,
      metadata: {
        category: '',
        estimatedTime: 0
      },
      questions: []
    },
    mode: 'onBlur'
  });

  const { fields, append, remove, move, replace } = useFieldArray({
    control,
    name: 'questions'
  });

  useEffect(() => {
    if (templateId) {
      setIsEditing(true);
      fetchTemplate();
    } else {
      setLoading(false);
    }
  }, [templateId]);

  const fetchTemplate = async () => {
    if (!templateId) return;
    try {
      setLoading(true);
      const template = await getTemplateById(templateId);
      if (template) {
        const formData: TemplateFormData = {
          ...template,
          questions: (template.questions || []).map((q: Question) => ({
            ...q,
            optionsString: q.options?.join(', ') || ''
          }))
        };
        reset(formData);
        if (template.coverImage) {
          setCoverImageUrl(template.coverImage);
        }
      } else {
        console.error(`Template with ID ${templateId} not found.`);
      }
    } catch (error: any) {
      console.error('Error fetching template:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleImageUpload = (url: string) => {
    setCoverImageUrl(url);
    setImageUploadModalOpen(false);
  };

  const openImageUploadModal = () => {
    setImageUploadModalOpen(true);
  };

  const closeImageUploadModal = () => {
    setImageUploadModalOpen(false);
  };

  // AI Generation functions
  const openAiGenerationModal = () => {
    setAiGenerationModalOpen(true);
    setGenerationError(null);
  };

  const closeAiGenerationModal = () => {
    setAiGenerationModalOpen(false);
    setAiDescription('');
    setGenerationError(null);
  };

  // This function simulates AI generation with a mock implementation
  // In a real application, this would make an API call to an AI service
  const generateWithAI = async () => {
    if (!aiDescription.trim()) {
      setGenerationError('Please provide a description to generate questions.');
      return;
    }

    setIsGenerating(true);
    setGenerationError(null);

    try {
      // Simulate API call delay
      await new Promise((resolve) => setTimeout(resolve, 1500));

      // This is a mock implementation that creates sample questions based on description
      // Replace this with your actual AI generation logic
      const keywords = aiDescription.toLowerCase();
      let generatedQuestions: Array<Question & { optionsString?: string }> = [];

      // Generate different types of questions based on keywords in the description
      if (keywords.includes('health') || keywords.includes('medical')) {
        generatedQuestions = [
          {
            id: `ai-${Date.now()}-1`,
            text: 'How would you rate your overall health?',
            questionType: QuestionType.SCALE,
            isOptional: false,
            order: 1,
            disabled: false,
            min: 1,
            max: 10
          },
          {
            id: `ai-${Date.now()}-2`,
            text: 'Do you have any pre-existing medical conditions?',
            questionType: QuestionType.BOOLEAN,
            isOptional: false,
            order: 2,
            disabled: false
          },
          {
            id: `ai-${Date.now()}-3`,
            text: 'Which health issues are you currently experiencing?',
            questionType: QuestionType.CHECKBOX,
            isOptional: true,
            order: 3,
            disabled: false,
            optionsString: 'Headache, Fatigue, Stress, Anxiety, Sleep problems, Other'
          }
        ];
      } else if (keywords.includes('satisfaction') || keywords.includes('feedback')) {
        generatedQuestions = [
          {
            id: `ai-${Date.now()}-1`,
            text: 'How satisfied are you with our service?',
            questionType: QuestionType.SCALE,
            isOptional: false,
            order: 1,
            disabled: false,
            min: 1,
            max: 5
          },
          {
            id: `ai-${Date.now()}-2`,
            text: 'Would you recommend our service to others?',
            questionType: QuestionType.SELECT,
            isOptional: false,
            order: 2,
            disabled: false,
            optionsString: 'Definitely yes, Probably yes, Not sure, Probably not, Definitely not'
          },
          {
            id: `ai-${Date.now()}-3`,
            text: 'What aspects of our service could be improved?',
            questionType: QuestionType.TEXT,
            isOptional: true,
            order: 3,
            disabled: false
          }
        ];
      } else {
        // Default general questions
        generatedQuestions = [
          {
            id: `ai-${Date.now()}-1`,
            text: `What are your thoughts about ${aiDescription}?`,
            questionType: QuestionType.TEXT,
            isOptional: false,
            order: 1,
            disabled: false
          },
          {
            id: `ai-${Date.now()}-2`,
            text: `How important is ${aiDescription} to you?`,
            questionType: QuestionType.SCALE,
            isOptional: false,
            order: 2,
            disabled: false,
            min: 1,
            max: 10
          },
          {
            id: `ai-${Date.now()}-3`,
            text: `Which aspects of ${aiDescription} are you most interested in?`,
            questionType: QuestionType.CHECKBOX,
            isOptional: true,
            order: 3,
            disabled: false,
            optionsString: 'Quality, Cost, Accessibility, Effectiveness, Other'
          }
        ];
      }

      // Update form with generated questions (either replace existing or append)
      if (fields.length === 0 || confirm('Replace existing questions with AI-generated ones?')) {
        replace(generatedQuestions);
      } else {
        // Append questions with updated order
        generatedQuestions.forEach((question, index) => {
          append({
            ...question,
            order: fields.length + index + 1
          });
        });
      }

      // If there's no title/name yet, suggest one based on the description
      if (!watch('name')) {
        setValue('name', `${aiDescription.charAt(0).toUpperCase() + aiDescription.slice(1)} Questionnaire`);
      }

      // Close modal after successful generation
      closeAiGenerationModal();
    } catch (error) {
      console.error('Error generating with AI:', error);
      setGenerationError('An error occurred while generating. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  const onSubmit = async (data: TemplateFormData) => {
    setIsSubmitting(true);
    try {
      // Ensure title is set to name if not provided
      const title = data.title || data.name;

      const dataWithMetadata = {
        ...data,
        title,
        coverImage: coverImageUrl || undefined,
        metadata: data.metadata || { category: '', estimatedTime: 0 },
        version: data.version || 1
      };

      const dataToSave: QuestionnaireTemplate = {
        ...dataWithMetadata,
        questions: dataWithMetadata.questions.map((q, index) => {
          const { optionsString, ...rest } = q;
          return {
            ...rest,
            order: index + 1,
            options:
              (q.questionType === QuestionType.SELECT || q.questionType === QuestionType.CHECKBOX) && optionsString
                ? optionsString
                    .split(',')
                    .map((opt) => opt.trim())
                    .filter(Boolean)
                : rest.options || []
          };
        })
      };

      if (isEditing && templateId) {
        await updateTemplate(templateId, dataToSave);
      } else {
        await createTemplate(dataToSave);
      }

      // Navigate to AllTemplates page after successful save
      navigate('/trq/templates');
    } catch (error) {
      console.error('Error saving template:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleAddQuestion = () => {
    const newQuestion: Question & { optionsString?: string } = {
      id: Date.now().toString(),
      text: '',
      questionType: QuestionType.TEXT,
      isOptional: false,
      optionsString: '',
      order: fields.length + 1,
      disabled: false
    };
    append(newQuestion);
  };

  const handleDeleteQuestion = (index: number) => {
    setQuestionToDelete(index);
    setDeleteDialogOpen(true);
  };

  const confirmDeleteQuestion = () => {
    if (questionToDelete !== null) {
      remove(questionToDelete);
      setDeleteDialogOpen(false);
      setQuestionToDelete(null);
    }
  };

  const QuestionFormFields = ({ index }: { index: number }) => {
    const questionType = watch(`questions.${index}.questionType` as const);
    const showOptions = questionType === QuestionType.SELECT || questionType === QuestionType.CHECKBOX;

    return (
      <Paper key={fields[index].id} sx={{ p: 2, mb: 2 }}>
        <Grid container spacing={2} alignItems="center" justifyContent="center">
          <Grid item xs={12} sm={6} md={4}>
            <Controller
              name={`questions.${index}.text` as const}
              control={control}
              rules={{ required: 'Question text is required' }}
              render={({ field }) => (
                <TextField
                  {...field}
                  fullWidth
                  label={`Question ${index + 1}`}
                  error={!!errors.questions?.[index]?.text}
                  helperText={errors.questions?.[index]?.text?.message as string}
                />
              )}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <FormControl fullWidth>
              <InputLabel>Question Type</InputLabel>
              <Controller
                name={`questions.${index}.questionType` as const}
                control={control}
                render={({ field }) => (
                  <Select {...field} label="Question Type" defaultValue={QuestionType.TEXT}>
                    {questionTypes.map((type) => (
                      <MenuItem key={type.value} value={type.value}>
                        {type.label}
                      </MenuItem>
                    ))}
                  </Select>
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={showOptions ? 6 : 3} md={showOptions ? 3 : 2}>
            <FormControlLabel
              control={
                <Controller
                  name={`questions.${index}.isOptional` as const}
                  control={control}
                  render={({ field }) => <Switch {...field} checked={field.value} onChange={(e) => field.onChange(e.target.checked)} />}
                />
              }
              label="Optional"
              sx={{ pt: showOptions ? 0 : 3.5 }}
            />
          </Grid>

          {showOptions && (
            <Grid item xs={12} sm={6} md={5}>
              <Controller
                name={`questions.${index}.optionsString` as const}
                control={control}
                rules={{
                  required: showOptions ? 'Options are required for this question type' : false,
                  validate: (value) =>
                    !showOptions ||
                    (value &&
                      value
                        .split(',')
                        .map((s: string) => s.trim())
                        .filter((s: string) => s).length > 0) ||
                    'Please enter at least one option'
                }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="Options (comma-separated)"
                    error={!!errors.questions?.[index]?.optionsString}
                    helperText={(errors.questions?.[index]?.optionsString?.message as string) || 'E.g., Yes, No, Maybe'}
                  />
                )}
              />
            </Grid>
          )}

          <Grid item xs={12} sm={showOptions ? 12 : 3} md={2} sx={{ mt: { xs: 1, sm: 0 } }}>
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1, pt: showOptions ? 0 : 3 }}>
              <Tooltip title="Move Up">
                <IconButton size="small" disabled={index === 0} onClick={() => move(index, index - 1)}>
                  <MoveUpIcon fontSize="small" />
                </IconButton>
              </Tooltip>
              <Tooltip title="Move Down">
                <IconButton size="small" disabled={index === fields.length - 1} onClick={() => move(index, index + 1)}>
                  <MoveDownIcon fontSize="small" />
                </IconButton>
              </Tooltip>
              <Tooltip title="Delete">
                <IconButton size="small" color="error" onClick={() => handleDeleteQuestion(index)}>
                  <DeleteIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Box>
          </Grid>
        </Grid>
      </Paper>
    );
  };

  if (loading) {
    return (
      <MainCard title={isEditing ? 'Edit Template' : 'Create Template'}>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Skeleton variant="rectangular" height={56} />
          </Grid>
          <Grid item xs={12}>
            <Skeleton variant="rectangular" height={80} />
          </Grid>
          <Grid item xs={12} sm={6}>
            <Skeleton variant="rectangular" height={56} />
          </Grid>
          <Grid item xs={12} sm={6}>
            <Skeleton variant="rectangular" height={56} />
          </Grid>
          <Grid item xs={12}>
            <Skeleton variant="rectangular" height={42} width={180} />
          </Grid>
        </Grid>
        <Box sx={{ mt: 4 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Skeleton variant="text" width={100} height={32} />
            <Skeleton variant="rectangular" height={36} width={150} />
          </Box>
          <Skeleton variant="rectangular" height={100} sx={{ mb: 2 }} />
          <Skeleton variant="rectangular" height={100} />
        </Box>
      </MainCard>
    );
  }

  return (
    <MainCard title={isEditing ? 'Edit Template' : 'Create Template'}>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Controller
              name="name"
              control={control}
              rules={{ required: 'Template name is required' }}
              render={({ field }) => (
                <TextField
                  {...field}
                  fullWidth
                  label="Template Name"
                  error={!!errors.name}
                  helperText={errors.name?.message as string}
                  disabled={isSubmitting}
                />
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              name="description"
              control={control}
              render={({ field }) => <TextField {...field} fullWidth multiline rows={3} label="Description" disabled={isSubmitting} />}
            />
          </Grid>

          {/* Cover Image Upload Preview and Drop Section */}
          <Grid item xs={12}>
            <Typography variant="subtitle1" gutterBottom>
              Cover Image (Optional)
            </Typography>
            <Card
              variant="outlined"
              sx={{
                p: 2,
                borderStyle: 'dashed',
                borderColor: 'divider',
                bgcolor: 'background.paper',
                height: coverImageUrl ? 'auto' : 200,
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                cursor: 'pointer',
                transition: 'all 0.2s',
                '&:hover': {
                  borderColor: 'primary.main',
                  bgcolor: 'action.hover'
                },
                position: 'relative'
              }}
              onClick={openImageUploadModal}
            >
              {coverImageUrl ? (
                <>
                  <CardMedia
                    component="img"
                    image={coverImageUrl}
                    alt="Template Cover Preview"
                    sx={{
                      maxHeight: 260,
                      width: '100%',
                      objectFit: 'contain',
                      mb: 1
                    }}
                  />
                  <Box
                    sx={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      bgcolor: 'rgba(0,0,0,0.4)',
                      opacity: 0,
                      transition: 'opacity 0.2s',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      '&:hover': {
                        opacity: 1
                      }
                    }}
                  >
                    <Button
                      variant="contained"
                      startIcon={<PhotoCameraIcon />}
                      sx={{
                        bgcolor: 'rgba(255,255,255,0.9)',
                        color: 'text.primary',
                        '&:hover': {
                          bgcolor: 'white'
                        }
                      }}
                    >
                      Change Image
                    </Button>
                  </Box>
                </>
              ) : (
                <Box sx={{ textAlign: 'center', color: 'text.secondary' }}>
                  <CloudUploadIcon sx={{ fontSize: 60, mb: 1, color: 'action.active' }} />
                  <Typography variant="body1" gutterBottom>
                    Click to upload cover image
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    Recommended size: 1200 x 630 pixels (16:9 ratio)
                  </Typography>
                </Box>
              )}
            </Card>
          </Grid>

          <Grid item xs={12} sm={6}>
            <FormControl fullWidth disabled={isSubmitting}>
              <InputLabel>Category</InputLabel>
              <Controller
                name="metadata.category"
                control={control}
                render={({ field }) => (
                  <Select {...field} label="Category" defaultValue="">
                    <MenuItem value="" disabled>
                      <em>Select a category...</em>
                    </MenuItem>
                    <MenuItem value="general">General</MenuItem>
                    <MenuItem value="medical">Medical</MenuItem>
                    <MenuItem value="psychological">Psychological</MenuItem>
                    <MenuItem value="lifestyle">Lifestyle</MenuItem>
                  </Select>
                )}
              />
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6}>
            <Controller
              name="metadata.estimatedTime"
              control={control}
              rules={{
                min: { value: 0, message: 'Time must be positive' }
              }}
              render={({ field }) => (
                <TextField
                  {...field}
                  fullWidth
                  type="number"
                  label="Estimated Time (minutes)"
                  InputProps={{ inputProps: { min: 0 } }}
                  onChange={(e) => field.onChange(Number(e.target.value))}
                  error={!!errors.metadata?.estimatedTime}
                  helperText={errors.metadata?.estimatedTime?.message as string}
                  disabled={isSubmitting}
                />
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              name="isPublished"
              control={control}
              render={({ field }) => (
                <FormControlLabel
                  control={
                    <Switch {...field} checked={field.value} onChange={(e) => field.onChange(e.target.checked)} disabled={isSubmitting} />
                  }
                  label="Publish Template"
                />
              )}
            />
          </Grid>
        </Grid>

        <Box sx={{ mt: 4 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h5">Questions</Typography>
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Button
                variant="outlined"
                color="secondary"
                startIcon={<AutoAwesomeIcon />}
                onClick={openAiGenerationModal}
                disabled={isSubmitting}
              >
                Generate with AI
              </Button>
              <Button variant="contained" startIcon={<AddIcon />} onClick={handleAddQuestion} disabled={isSubmitting}>
                Add Question
              </Button>
            </Box>
          </Box>

          {fields.length === 0 ? (
            <Paper sx={{ p: 3, textAlign: 'center', border: '1px dashed', borderColor: 'grey.400' }}>
              <Typography color="text.secondary" paragraph>
                No questions added yet. Click &apos;Add Question&apos; to start building your template manually.
              </Typography>
              <Divider sx={{ my: 2 }}>
                <Typography variant="caption" color="text.secondary">
                  OR
                </Typography>
              </Divider>
              <Button variant="outlined" color="secondary" startIcon={<AutoAwesomeIcon />} onClick={openAiGenerationModal} sx={{ mt: 1 }}>
                Generate Questions with AI
              </Button>
            </Paper>
          ) : (
            fields.map((field, index) => <QuestionFormFields key={field.id} index={index} />)
          )}
        </Box>

        <Box sx={{ mt: 4, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
          <Button variant="outlined" onClick={() => navigate('/trq/templates')} disabled={isSubmitting}>
            Cancel
          </Button>
          <Button
            type="submit"
            variant="contained"
            disabled={isSubmitting}
            startIcon={isSubmitting ? <CircularProgress size={20} color="inherit" /> : <SaveIcon />}
          >
            {isSubmitting ? (isEditing ? 'Saving...' : 'Creating...') : isEditing ? 'Save Changes' : 'Create Template'}
          </Button>
        </Box>
      </form>

      {/* Delete Question Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Delete Question</DialogTitle>
        <DialogContent>
          <Typography>Are you sure you want to delete this question? This action cannot be undone.</Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button onClick={confirmDeleteQuestion} color="error">
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      {/* Image Upload Modal */}
      <Dialog open={imageUploadModalOpen} onClose={closeImageUploadModal} maxWidth="md" fullWidth>
        <DialogTitle>Upload Cover Image</DialogTitle>
        <DialogContent>
          <Box sx={{ p: 2 }}>
            <Typography variant="body2" color="text.secondary" paragraph>
              Upload an image to use as cover for this template. Ideal dimensions are 1200 x 630 pixels (16:9 ratio).
            </Typography>
            <ImageUploader
              storagePathFn={(file) => `questionnaire-templates/${templateId || 'new'}-${Date.now()}-cover.${file.name.split('.').pop()}`}
              onUpload={handleImageUpload}
              label="Upload Cover Image"
              buttonText="Select Image"
              acceptedTypes="image/*"
              disabled={isSubmitting}
              maxFileSizeMB={2}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={closeImageUploadModal}>Cancel</Button>
        </DialogActions>
      </Dialog>

      {/* AI Generation Modal */}
      <Dialog open={aiGenerationModalOpen} onClose={closeAiGenerationModal} maxWidth="md" fullWidth>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <AutoAwesomeIcon color="secondary" />
            <Typography variant="h6">Generate with AI</Typography>
          </Box>
        </DialogTitle>
        <DialogContent>
          <Box sx={{ p: 2 }}>
            <Typography variant="body2" paragraph>
              Describe what kind of questionnaire you want to create, and our AI will generate relevant questions for you.
            </Typography>

            <TextField
              fullWidth
              multiline
              rows={4}
              label="Describe your questionnaire"
              value={aiDescription}
              onChange={(e) => setAiDescription(e.target.value)}
              placeholder="Example: A health assessment questionnaire to evaluate patient wellness and identify potential areas of concern."
              helperText="The more details you provide, the better the generated questions will be."
              disabled={isGenerating}
              sx={{ mb: 2 }}
            />

            {generationError && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {generationError}
              </Alert>
            )}

            <Card variant="outlined" sx={{ p: 2, bgcolor: 'background.default' }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                <PsychologyIcon color="primary" />
                <Typography variant="subtitle2">AI Assistant Tips</Typography>
              </Box>
              <Typography variant="body2" color="text.secondary">
                For best results, include:
              </Typography>
              <ul style={{ margin: '8px 0', paddingLeft: '1.5rem' }}>
                <li>
                  <Typography variant="body2" color="text.secondary">
                    Purpose of the questionnaire (e.g., health assessment, customer satisfaction)
                  </Typography>
                </li>
                <li>
                  <Typography variant="body2" color="text.secondary">
                    Target audience (e.g., patients, customers, employees)
                  </Typography>
                </li>
                <li>
                  <Typography variant="body2" color="text.secondary">
                    Specific topics to cover (e.g., sleep habits, work satisfaction)
                  </Typography>
                </li>
              </ul>
            </Card>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={closeAiGenerationModal} disabled={isGenerating}>
            Cancel
          </Button>
          <Button
            variant="contained"
            color="secondary"
            onClick={generateWithAI}
            disabled={isGenerating || !aiDescription.trim()}
            startIcon={isGenerating ? <CircularProgress size={20} color="inherit" /> : <AutoAwesomeIcon />}
          >
            {isGenerating ? 'Generating...' : 'Generate Questions'}
          </Button>
        </DialogActions>
      </Dialog>
    </MainCard>
  );
};

export default CreateTemplate;
