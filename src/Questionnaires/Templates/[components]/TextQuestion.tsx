import React from 'react';
import { Text<PERSON>ield, FormControl, FormHelperText } from '@mui/material';
import { Controller } from 'react-hook-form';

interface TextQuestionProps {
  name: string;
  control: any;
  label: string;
  required?: boolean;
  error?: boolean;
  helperText?: string;
  disabled?: boolean;
  defaultValue?: string;
}

const TextQuestion: React.FC<TextQuestionProps> = ({
  name,
  control,
  label,
  required = false,
  error = false,
  helperText,
  disabled = false,
  defaultValue = ''
}) => {
  return (
    <FormControl fullWidth error={error}>
      <Controller
        name={name}
        control={control}
        defaultValue={defaultValue}
        rules={{
          required: required ? 'This field is required' : false
        }}
        render={({ field }) => (
          <TextField
            {...field}
            label={label}
            variant="outlined"
            fullWidth
            required={required}
            disabled={disabled}
            error={error}
            multiline
            rows={4}
          />
        )}
      />
      {helperText && <FormHelperText>{helperText}</FormHelperText>}
    </FormControl>
  );
};

export default TextQuestion;
