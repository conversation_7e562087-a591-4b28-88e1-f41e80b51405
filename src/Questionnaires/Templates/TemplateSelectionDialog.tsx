import React, { useState } from 'react';
import { useIntl } from 'react-intl';

// material-ui
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Grid,
  Card,
  CardContent,
  CardActions,
  Typography,
  Chip,
  Box,
  Stack,
  IconButton,
  Tooltip
} from '@mui/material';

// icons
import DescriptionIcon from '@mui/icons-material/Description';
import CheckIcon from '@mui/icons-material/Check';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import GroupIcon from '@mui/icons-material/Group';
import CategoryIcon from '@mui/icons-material/Category';

interface Template {
  id: string;
  title: string;
  description: string;
  category: string;
  estimatedTime: string;
  questions: number;
  targetAudience: string;
}

interface TemplateSelectionDialogProps {
  open: boolean;
  onClose: () => void;
  onSelect: (templateId: string) => void;
}

const TemplateSelectionDialog = ({ open, onClose, onSelect }: TemplateSelectionDialogProps) => {
  const intl = useIntl();
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);

  // Comprehensive health assessment templates
  const templates: Template[] = [
    {
      id: 'general-health',
      title: 'General Health Assessment',
      description:
        'A comprehensive health assessment covering general wellness, lifestyle, and medical history. Includes sections on physical health, mental well-being, and lifestyle factors.',
      category: 'General Health',
      estimatedTime: '15-20 minutes',
      questions: 45,
      targetAudience: 'All patients'
    },
    {
      id: 'mental-health',
      title: 'Mental Health Screening',
      description:
        'Standardized screening for common mental health conditions including depression, anxiety, and stress. Based on validated clinical assessment tools.',
      category: 'Mental Health',
      estimatedTime: '10-15 minutes',
      questions: 30,
      targetAudience: 'Patients 18+'
    },
    {
      id: 'chronic-care',
      title: 'Chronic Condition Management',
      description:
        'Detailed assessment for patients with chronic conditions. Tracks symptoms, medication adherence, and quality of life indicators.',
      category: 'Chronic Care',
      estimatedTime: '20-25 minutes',
      questions: 50,
      targetAudience: 'Patients with chronic conditions'
    },
    {
      id: 'post-surgery',
      title: 'Post-Surgical Recovery',
      description: 'Monitors recovery progress, pain levels, and potential complications following surgical procedures.',
      category: 'Surgical Care',
      estimatedTime: '10 minutes',
      questions: 25,
      targetAudience: 'Post-surgical patients'
    },
    {
      id: 'pediatric',
      title: 'Pediatric Health Assessment',
      description: 'Child-specific health assessment focusing on growth, development, and common pediatric health concerns.',
      category: 'Pediatrics',
      estimatedTime: '15 minutes',
      questions: 35,
      targetAudience: 'Children 0-18 years'
    },
    {
      id: 'geriatric',
      title: 'Geriatric Health Assessment',
      description: 'Comprehensive assessment for older adults, including fall risk, cognitive function, and activities of daily living.',
      category: 'Geriatrics',
      estimatedTime: '20 minutes',
      questions: 40,
      targetAudience: 'Adults 65+'
    },
    {
      id: 'nutrition',
      title: 'Nutritional Assessment',
      description: 'Evaluates dietary habits, nutritional status, and identifies potential nutritional deficiencies or concerns.',
      category: 'Nutrition',
      estimatedTime: '15 minutes',
      questions: 30,
      targetAudience: 'All patients'
    },
    {
      id: 'lifestyle',
      title: 'Lifestyle & Wellness',
      description: 'Assesses physical activity, sleep patterns, stress management, and overall wellness habits.',
      category: 'Wellness',
      estimatedTime: '15 minutes',
      questions: 35,
      targetAudience: 'All patients'
    }
  ];

  const handleTemplateSelect = (templateId: string) => {
    setSelectedTemplate(templateId);
  };

  const handleStart = () => {
    if (selectedTemplate) {
      onSelect(selectedTemplate);
      onClose();
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      // Added data-testid to dialog
      data-testid="template-selection-dialog"
    >
      <DialogTitle>
        <Typography variant="h4">Select Questionnaire Template</Typography>
        <Typography variant="subtitle1" color="text.secondary">
          Choose a template to start a new questionnaire
        </Typography>
      </DialogTitle>
      <DialogContent>
        <Grid container spacing={3}>
          {templates.map((template) => (
            <Grid item xs={12} sm={6} md={4} key={template.id}>
              <Card
                sx={{
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  border: selectedTemplate === template.id ? '2px solid' : '1px solid',
                  borderColor: selectedTemplate === template.id ? 'primary.main' : 'divider',
                  '&:hover': {
                    boxShadow: 3,
                    cursor: 'pointer'
                  }
                }}
                onClick={() => handleTemplateSelect(template.id)}
                // Added dynamic data-testid to card
                data-testid={`template-card-${template.id}`}
              >
                <CardContent sx={{ flexGrow: 1 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <DescriptionIcon color="primary" sx={{ mr: 1 }} />
                    <Typography variant="h6" component="div">
                      {template.title}
                    </Typography>
                  </Box>

                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    {template.description}
                  </Typography>

                  <Stack direction="row" spacing={1} sx={{ mb: 2 }}>
                    <Chip icon={<CategoryIcon />} label={template.category} size="small" color="primary" variant="outlined" />
                    <Chip icon={<GroupIcon />} label={template.targetAudience} size="small" color="info" variant="outlined" />
                  </Stack>

                  <Box sx={{ display: 'flex', alignItems: 'center', color: 'text.secondary' }}>
                    <AccessTimeIcon fontSize="small" sx={{ mr: 0.5 }} />
                    <Typography variant="caption">
                      {template.estimatedTime} • {template.questions} questions
                    </Typography>
                  </Box>
                </CardContent>
                <CardActions sx={{ justifyContent: 'flex-end', p: 2 }}>
                  {selectedTemplate === template.id && (
                    <Tooltip title="Selected">
                      <IconButton color="primary">
                        <CheckIcon />
                      </IconButton>
                    </Tooltip>
                  )}
                </CardActions>
              </Card>
            </Grid>
          ))}
        </Grid>
      </DialogContent>
      <DialogActions sx={{ p: 3 }}>
        <Button onClick={onClose}>{intl.formatMessage({ id: 'cancel' }) || 'Cancel'}</Button>
        <Button
          variant="contained"
          color="primary"
          onClick={handleStart}
          disabled={!selectedTemplate}
          // Added data-testid to confirm button
          data-testid="dialog-create-questionnaire-button"
        >
          {intl.formatMessage({ id: 'create-questionnaire' }) || 'Create Questionnaire'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default TemplateSelectionDialog;
