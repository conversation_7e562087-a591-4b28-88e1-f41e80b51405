import { useEffect } from 'react';
import { useQuestionnaireWizard } from './QuestionnaireWizardContext';

interface UseKeyboardNavigationProps {
  onSubmit: () => void;
  isValid: boolean;
  isSubmitting: boolean;
}

export const useKeyboardNavigation = ({ onSubmit, isValid, isSubmitting }: UseKeyboardNavigationProps) => {
  const { goNext, goBack, isLastStep, isFirstStep } = useQuestionnaireWizard();

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Don't handle key events if user is typing in an input field
      const target = event.target as HTMLElement;
      if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.isContentEditable) {
        return;
      }

      switch (event.key) {
        case 'ArrowRight':
        case 'Tab':
          if (!event.shiftKey && !isSubmitting && isValid && !isLastStep) {
            event.preventDefault();
            goNext();
          }
          break;
        case 'ArrowLeft':
        case 'Backspace':
          if (!isFirstStep && !isSubmitting) {
            event.preventDefault();
            goBack();
          }
          break;
        case 'Enter':
          if (isLastStep && isValid && !isSubmitting) {
            event.preventDefault();
            onSubmit();
          } else if (!isLastStep && isValid && !isSubmitting) {
            event.preventDefault();
            goNext();
          }
          break;
        default:
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [goNext, goBack, isLastStep, isFirstStep, isValid, isSubmitting, onSubmit]);
};
