import React, { useState } from 'react';
import {
  Box,
  IconButton,
  Tooltip,
  Fade,
  Paper,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Chip
} from '@mui/material';
import KeyboardIcon from '@mui/icons-material/Keyboard';
import CloseIcon from '@mui/icons-material/Close';
import { motion, AnimatePresence } from 'framer-motion';

export const KeyboardShortcutsHelp: React.FC = () => {
  const [open, setOpen] = useState(false);

  const toggleOpen = () => {
    setOpen(!open);
  };

  const shortcuts = [
    { key: 'Right Arrow / Tab', action: 'Next question' },
    { key: 'Left Arrow / Backspace', action: 'Previous question' },
    { key: 'Enter', action: 'Submit form (on last question) or next question' }
  ];

  return (
    <Box sx={{ position: 'fixed', bottom: 16, right: 16, zIndex: 1000 }}>
      <Tooltip title="Keyboard Shortcuts" placement="left" TransitionComponent={Fade} TransitionProps={{ timeout: 600 }}>
        <IconButton
          aria-label="keyboard shortcuts"
          onClick={toggleOpen}
          component={motion.button}
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          sx={{
            backgroundColor: 'primary.main',
            color: 'primary.contrastText',
            '&:hover': {
              backgroundColor: 'primary.dark'
            },
            boxShadow: 3
          }}
        >
          <KeyboardIcon />
        </IconButton>
      </Tooltip>

      <AnimatePresence>
        {open && (
          <motion.div
            initial={{ opacity: 0, y: 20, scale: 0.8 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 20, scale: 0.8 }}
            transition={{ type: 'spring', stiffness: 400, damping: 25 }}
            style={{
              position: 'absolute',
              bottom: '60px',
              right: 0,
              width: '300px',
              zIndex: 1000
            }}
          >
            <Paper
              elevation={8}
              sx={{
                p: 2,
                borderRadius: 2,
                backgroundColor: (theme) => (theme.palette.mode === 'dark' ? 'background.paper' : 'background.default')
              }}
            >
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                <Typography variant="h6" component="h2" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <KeyboardIcon fontSize="small" />
                  Keyboard Shortcuts
                </Typography>
                <IconButton size="small" onClick={toggleOpen} aria-label="close shortcuts panel">
                  <CloseIcon fontSize="small" />
                </IconButton>
              </Box>

              <TableContainer component={Box}>
                <Table size="small">
                  <TableBody>
                    {shortcuts.map((shortcut) => (
                      <TableRow key={shortcut.key}>
                        <TableCell>
                          <Chip
                            label={shortcut.key}
                            size="small"
                            variant="outlined"
                            sx={{
                              fontFamily: 'monospace',
                              fontWeight: 'bold'
                            }}
                          />
                        </TableCell>
                        <TableCell>{shortcut.action}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>

              <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 1, textAlign: 'center' }}>
                Enhance your experience with keyboard navigation
              </Typography>
            </Paper>
          </motion.div>
        )}
      </AnimatePresence>
    </Box>
  );
};
