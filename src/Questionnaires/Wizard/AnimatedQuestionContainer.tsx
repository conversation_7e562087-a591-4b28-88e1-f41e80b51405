import React, { useState } from 'react';
import { motion, AnimatePresence, PanInfo } from 'framer-motion';
import { Box } from '@mui/material';
import { useQuestionnaireWizard } from './QuestionnaireWizardContext';
import { QuestionStep } from 'Questionnaires/[components]/QuestionStep';
import { Control, FieldErrors } from 'react-hook-form';

interface AnimatedQuestionContainerProps {
  control: Control<any>;
  errors: FieldErrors<any>;
}

export const AnimatedQuestionContainer: React.FC<AnimatedQuestionContainerProps> = ({ control, errors }) => {
  const { activeStep, currentQuestion, direction, goNext, goBack, isFirstStep, isLastStep } = useQuestionnaireWizard();
  const [isDragging, setIsDragging] = useState(false);

  // Handle swipe gestures
  const handleDragEnd = (event: MouseEvent | TouchEvent | PointerEvent, info: PanInfo) => {
    const threshold = 100; // Minimum swipe distance to trigger navigation

    // Only handle horizontal swipes (x-axis)
    if (Math.abs(info.offset.x) > threshold) {
      // Left swipe
      if (info.offset.x < 0 && !isLastStep) {
        goNext();
      }
      // Right swipe
      else if (info.offset.x > 0 && !isFirstStep) {
        goBack();
      }
    }

    setIsDragging(false);
  };

  // Slide variants for animation
  const slideVariants = {
    enterFromRight: {
      x: 300,
      opacity: 0
    },
    enterFromLeft: {
      x: -300,
      opacity: 0
    },
    center: {
      x: 0,
      opacity: 1
    },
    exitToLeft: {
      x: -300,
      opacity: 0
    },
    exitToRight: {
      x: 300,
      opacity: 0
    }
  };

  if (!currentQuestion) return null;

  return (
    <Box
      sx={{
        position: 'relative',
        overflow: 'hidden',
        minHeight: '300px',
        cursor: isDragging ? 'grabbing' : 'default'
      }}
    >
      <AnimatePresence initial={false} mode="wait" custom={direction}>
        <motion.div
          key={activeStep}
          custom={direction}
          variants={slideVariants}
          initial={direction === 'forward' ? 'enterFromRight' : 'enterFromLeft'}
          animate="center"
          exit={direction === 'forward' ? 'exitToLeft' : 'exitToRight'}
          transition={{
            x: { type: 'spring', stiffness: 300, damping: 30 },
            opacity: { duration: 0.2 }
          }}
          drag="x"
          dragConstraints={{ left: 0, right: 0 }}
          dragElastic={0.2}
          onDragStart={() => setIsDragging(true)}
          onDragEnd={handleDragEnd}
          style={{
            width: '100%',
            position: 'absolute'
          }}
        >
          <QuestionStep question={currentQuestion} control={control} errors={errors} />
        </motion.div>
      </AnimatePresence>
    </Box>
  );
};
