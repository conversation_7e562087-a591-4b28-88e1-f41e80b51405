import React from 'react';
import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, StepL<PERSON>l, LinearProgress } from '@mui/material';
import KeyboardArrowLeftIcon from '@mui/icons-material/KeyboardArrowLeft';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import CheckIcon from '@mui/icons-material/Check';
import { useQuestionnaireWizard } from './QuestionnaireWizardContext';
import { motion } from 'framer-motion';
import { AnimatedProgressBar } from './AnimatedProgressBar';

interface StepperNavigationProps {
  onSubmit: () => void;
  isValid: boolean;
  isSubmitting: boolean;
}

export const StepperNavigation: React.FC<StepperNavigationProps> = ({ onSubmit, isValid, isSubmitting }) => {
  const { activeStep, goNext, goBack, isFirstStep, isLastStep, questions } = useQuestionnaireWizard();

  const steps = questions.map((question, index) => ({
    label: `Question ${index + 1}`,
    question
  }));

  return (
    <Box sx={{ my: 4 }}>
      {/* Enhanced animated progress bar */}
      <AnimatedProgressBar height={10} color="primary.main" />

      {/* Stepper */}
      <Stepper
        activeStep={activeStep}
        alternativeLabel
        sx={{
          mb: 4,
          '& .MuiStepConnector-line': {
            transition: 'border-color 0.3s ease'
          }
        }}
      >
        {steps.map((step, index) => (
          <Step key={index} completed={index < activeStep}>
            <StepLabel
              StepIconProps={{
                sx: {
                  transition: 'color 0.3s ease, transform 0.2s ease',
                  '&.Mui-active, &.Mui-completed': {
                    transform: 'scale(1.1)'
                  }
                }
              }}
            >
              {step.label}
            </StepLabel>
          </Step>
        ))}
      </Stepper>

      {/* Navigation buttons */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>
        <Button
          component={motion.button}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          disabled={isFirstStep}
          onClick={goBack}
          startIcon={<KeyboardArrowLeftIcon />}
          sx={{
            transition: 'all 0.3s ease',
            opacity: isFirstStep ? 0.5 : 1
          }}
        >
          Back
        </Button>

        {isLastStep ? (
          <Button
            component={motion.button}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            variant="contained"
            color="primary"
            onClick={onSubmit}
            endIcon={<CheckIcon />}
            disabled={!isValid || isSubmitting}
            sx={{
              transition: 'all 0.3s ease',
              position: 'relative',
              overflow: 'hidden'
            }}
          >
            {isSubmitting ? 'Submitting...' : 'Submit'}
            {isSubmitting && (
              <LinearProgress
                sx={{
                  position: 'absolute',
                  bottom: 0,
                  left: 0,
                  right: 0,
                  height: 4
                }}
              />
            )}
          </Button>
        ) : (
          <Button
            component={motion.button}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            variant="contained"
            color="primary"
            onClick={goNext}
            endIcon={<KeyboardArrowRightIcon />}
            disabled={!isValid}
            sx={{ transition: 'all 0.3s ease' }}
          >
            Next
          </Button>
        )}
      </Box>
    </Box>
  );
};
