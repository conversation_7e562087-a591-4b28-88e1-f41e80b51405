import React, { useEffect } from 'react';
import { Box, Typography } from '@mui/material';
import { motion, useAnimation } from 'framer-motion';
import { useQuestionnaireWizard } from './QuestionnaireWizardContext';

interface AnimatedProgressBarProps {
  showPercentage?: boolean;
  height?: number;
  color?: string;
}

export const AnimatedProgressBar: React.FC<AnimatedProgressBarProps> = ({ showPercentage = true, height = 8, color = 'primary.main' }) => {
  const { activeStep, totalSteps } = useQuestionnaireWizard();
  const controls = useAnimation();

  // Calculate progress as a percentage
  const progress = totalSteps > 0 ? ((activeStep + 1) / totalSteps) * 100 : 0;

  // Animate the progress bar whenever the activeStep changes
  useEffect(() => {
    controls.start({
      width: `${progress}%`,
      transition: {
        type: 'spring',
        stiffness: 50,
        damping: 15
      }
    });
  }, [activeStep, progress, controls]);

  return (
    <Box sx={{ mb: 3, width: '100%' }}>
      {showPercentage && (
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
          <Typography variant="body2" color="text.secondary">
            Question {activeStep + 1} of {totalSteps}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {Math.round(progress)}% Complete
          </Typography>
        </Box>
      )}

      <Box
        sx={{
          width: '100%',
          height: height,
          backgroundColor: 'background.paper',
          borderRadius: height,
          boxShadow: 'inset 0 2px 4px rgba(0,0,0,0.1)',
          overflow: 'hidden',
          position: 'relative'
        }}
      >
        <motion.div
          initial={{ width: 0 }}
          animate={controls}
          style={{
            height: '100%',
            borderRadius: height,
            position: 'absolute',
            left: 0,
            top: 0,
            background: `linear-gradient(90deg, ${color} 0%, ${color} 70%, rgba(255,255,255,0.2) 100%)`
          }}
        />

        {/* Add pulse effect at the end of the progress bar */}
        <motion.div
          style={{
            width: height * 1.5,
            height: height * 1.5,
            borderRadius: '50%',
            position: 'absolute',
            top: '50%',
            left: `${progress}%`,
            transform: 'translate(-50%, -50%)',
            background: color,
            opacity: 0.5,
            zIndex: 2
          }}
          animate={{
            scale: [1, 1.3, 1],
            opacity: [0.5, 0.3, 0.5]
          }}
          transition={{
            duration: 2,
            ease: 'easeInOut',
            repeat: Infinity
          }}
        />
      </Box>
    </Box>
  );
};
