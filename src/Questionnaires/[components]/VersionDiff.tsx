import React from 'react';
import { Box, Typography, Divider, List, ListItem, ListItemText, Chip } from '@mui/material';
import { TemplateVersion } from '../[types]/TemplateVersion'; // Corrected path
import { diffWords, Change } from 'diff';
import { Question } from 'Questionnaires/[types]/Question';

interface VersionDiffProps {
  version1: TemplateVersion;
  version2: TemplateVersion;
}

export const VersionDiff: React.FC<VersionDiffProps> = ({ version1, version2 }) => {
  const renderTextDiff = (text1: string, text2: string) => {
    const differences = diffWords(text1 || '', text2 || '');

    return (
      <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
        {differences.map((part: Change, index: number) => (
          <Typography
            key={index}
            component="span"
            sx={{
              backgroundColor: part.added ? 'success.light' : part.removed ? 'error.light' : 'transparent',
              color: part.added || part.removed ? 'common.white' : 'inherit'
            }}
          >
            {part.value}
          </Typography>
        ))}
      </Box>
    );
  };

  const renderQuestionDiff = () => {
    const questions1 = version1.questions || [];
    const questions2 = version2.questions || [];

    // Create a unique list of question IDs from both versions
    const allQuestionIds = new Set<string>();
    questions1.forEach((q: Question) => allQuestionIds.add(q.id));
    questions2.forEach((q: Question) => allQuestionIds.add(q.id));

    const allQuestionIdsList = Array.from(allQuestionIds);

    return (
      <List>
        {allQuestionIdsList
          .map((questionId, index) => {
            const q1 = questions1.find((q) => q.id === questionId);
            const q2 = questions2.find((q) => q.id === questionId);

            if (!q1 && q2) {
              return (
                <ListItem key={index}>
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography>Added: {q2.text}</Typography>
                        <Chip label="New" color="success" size="small" />
                      </Box>
                    }
                  />
                </ListItem>
              );
            }

            if (q1 && !q2) {
              return (
                <ListItem key={index}>
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography>Removed: {q1.text}</Typography>
                        <Chip label="Removed" color="error" size="small" />
                      </Box>
                    }
                  />
                </ListItem>
              );
            }

            if (q1 && q2 && (q1.text !== q2.text || q1.questionType !== q2.questionType)) {
              return (
                <ListItem key={index}>
                  <ListItemText
                    primary={
                      <Box>
                        <Typography variant="subtitle2">Question {index + 1}</Typography>
                        <Box sx={{ display: 'flex', gap: 2 }}>
                          <Box sx={{ flex: 1 }}>
                            <Typography variant="body2" color="text.secondary">
                              Version {version1.version}
                            </Typography>
                            {renderTextDiff(q1.text, q2.text)}
                          </Box>
                          <Box sx={{ flex: 1 }}>
                            <Typography variant="body2" color="text.secondary">
                              Version {version2.version}
                            </Typography>
                            {renderTextDiff(q2.text, q1.text)}
                          </Box>
                        </Box>
                      </Box>
                    }
                  />
                </ListItem>
              );
            }

            return null;
          })
          .filter(Boolean)}
      </List>
    );
  };

  return (
    <Box>
      <Box sx={{ mb: 2 }}>
        <Typography variant="h6">Name</Typography>
        {renderTextDiff(version1.name, version2.name)}
      </Box>

      <Divider sx={{ my: 2 }} />

      <Box sx={{ mb: 2 }}>
        <Typography variant="h6">Description</Typography>
        {renderTextDiff(version1.description || '', version2.description || '')}
      </Box>

      <Divider sx={{ my: 2 }} />

      <Box>
        <Typography variant="h6">Questions</Typography>
        {renderQuestionDiff()}
      </Box>
    </Box>
  );
};
