import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Tooltip
} from '@mui/material';
import { History as HistoryIcon, Compare as CompareIcon, Restore as RestoreIcon, Close as CloseIcon } from '@mui/icons-material';
import { format } from 'date-fns';
import { TemplateVersion } from '../[types]/TemplateVersion'; // Corrected path
import { useSnackbar } from 'notistack';
import { VersionDiff } from './VersionDiff';
import { getTemplateVersion, restoreTemplateVersion } from '../Templates/[services]/questionnaireTemplateService'; // Corrected path
interface VersionHistoryProps {
  templateId: string;
  versions: TemplateVersion[];
  onVersionRestored: () => void;
}

export const VersionHistory: React.FC<VersionHistoryProps> = ({ templateId, versions, onVersionRestored }) => {
  const { enqueueSnackbar } = useSnackbar();
  const [selectedVersions, setSelectedVersions] = useState<[string | null, string | null]>([null, null]);
  const [compareDialogOpen, setCompareDialogOpen] = useState(false);
  const [versionDetails, setVersionDetails] = useState<{
    version1: TemplateVersion | null;
    version2: TemplateVersion | null;
  }>({ version1: null, version2: null });
  const [restoreDialogOpen, setRestoreDialogOpen] = useState(false);
  const [versionToRestore, setVersionToRestore] = useState<string | null>(null);

  const handleVersionSelect = (versionId: string) => {
    if (selectedVersions[0] === null) {
      setSelectedVersions([versionId, null]);
    } else if (selectedVersions[1] === null) {
      setSelectedVersions([selectedVersions[0], versionId]);
    } else {
      setSelectedVersions([versionId, null]);
    }
  };

  const handleCompare = async () => {
    if (!selectedVersions[0] || !selectedVersions[1]) return;

    try {
      // Temporarily cast to any due to missing TemplateVersion type definition
      const [version1, version2] = await Promise.all([
        getTemplateVersion(templateId, selectedVersions[0]) as any, // Pass templateId
        getTemplateVersion(templateId, selectedVersions[1]) as any // Pass templateId
      ]);

      setVersionDetails({ version1, version2 });
      setCompareDialogOpen(true);
    } catch {
      enqueueSnackbar('Error loading version details', { variant: 'error' });
    }
  };

  const handleRestore = async () => {
    if (!versionToRestore) return;

    try {
      await restoreTemplateVersion(templateId, versionToRestore);
      enqueueSnackbar('Version restored successfully', { variant: 'success' });
      setRestoreDialogOpen(false);
      onVersionRestored();
    } catch {
      enqueueSnackbar('Error restoring version', { variant: 'error' });
    }
  };

  return (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <HistoryIcon sx={{ mr: 1 }} />
          <Typography variant="h6">Version History</Typography>
        </Box>

        <List>
          {versions.map((version) => (
            <ListItem
              key={version.version}
              sx={{
                cursor: 'pointer',
                backgroundColor: selectedVersions.includes(version.version.toString()) ? 'action.selected' : 'inherit'
              }}
              onClick={() => handleVersionSelect(version.version.toString())}
            >
              <ListItemText primary={`Version ${version.version}`} secondary={format(version.createdAt, 'PPP p')} />
              <ListItemSecondaryAction>
                <Tooltip title="Restore this version">
                  <IconButton
                    edge="end"
                    onClick={(e) => {
                      e.stopPropagation();
                      setVersionToRestore(version.version.toString());
                      setRestoreDialogOpen(true);
                    }}
                  >
                    <RestoreIcon />
                  </IconButton>
                </Tooltip>
              </ListItemSecondaryAction>
            </ListItem>
          ))}
        </List>

        {selectedVersions[0] && selectedVersions[1] && (
          <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center' }}>
            <Button variant="contained" startIcon={<CompareIcon />} onClick={handleCompare}>
              Compare Selected Versions
            </Button>
          </Box>
        )}
      </CardContent>

      <Dialog open={compareDialogOpen} onClose={() => setCompareDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            Version Comparison
            <IconButton onClick={() => setCompareDialogOpen(false)}>
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent>
          {versionDetails.version1 && versionDetails.version2 && (
            <VersionDiff version1={versionDetails.version1} version2={versionDetails.version2} />
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCompareDialogOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>

      <Dialog open={restoreDialogOpen} onClose={() => setRestoreDialogOpen(false)}>
        <DialogTitle>Restore Version</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to restore this version? This will create a new version with the content of the selected version.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setRestoreDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleRestore} color="primary" variant="contained">
            Restore
          </Button>
        </DialogActions>
      </Dialog>
    </Card>
  );
};
