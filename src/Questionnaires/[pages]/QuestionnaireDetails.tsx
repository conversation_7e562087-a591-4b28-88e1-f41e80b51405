import React, { useState, useEffect, useTransition } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useIntl } from 'react-intl';
import { Timestamp } from 'firebase/firestore';

// material-ui
import { Box, Button, Chip, CircularProgress, Divider, Grid, Stack, Typography, Tabs, Tab } from '@mui/material';

// project imports
import MainCard from '[components]/cards/MainCard';
import SubCard from '[components]/cards/SubCard';
import { gridSpacing } from '[constants]/gridSpacing';
import { getQuestionnaireById } from 'Questionnaires/[services]/questionnaireService';
import { Questionnaire } from 'Questionnaires/[types]/Questionnaire';
import Breadcrumbs from '[components]/extended/Breadcrumbs';
import { useAuth } from 'Authentication/[contexts]/AuthContext'; // Import useAuth hook
import { Role } from 'RBAC/[types]/Role'; // Import Role enum
import { getUserById } from 'Users/[services]/userService';
import { TRQUser } from 'Users/[types]/User'; // Import TRQUser for type safety
import ROUTES, { getDashboardUrlForRole } from 'Routing/appRoutes';

// icons
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import EditIcon from '@mui/icons-material/Edit';
import AssignmentIcon from '@mui/icons-material/Assignment';
import PersonIcon from '@mui/icons-material/Person';
import BusinessIcon from '@mui/icons-material/Business';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`questionnaire-tabpanel-${index}`}
      aria-labelledby={`questionnaire-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

// Helper function to get the correct questionnaires list route for the user's role
const getQuestionnairesListRoute = (role: Role | null | undefined): string => {
  switch (role) {
    case Role.Patient:
      return '/trq/my-questionnaires';
    default:
      return '/trq/questionnaires';
  }
};

const formatTimestamp = (timestamp: Timestamp | undefined): string => {
  if (!timestamp) return '';
  return timestamp.toDate().toLocaleDateString();
};

const getStatusColor = (
  status: Questionnaire['status']
): 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' => {
  switch (status) {
    case 'created':
      return 'default';
    case 'in-progress':
      return 'warning';
    case 'completed':
      return 'success';
    case 'reviewed':
      return 'info';
    default:
      return 'default';
  }
};

const QuestionnaireDetails = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const intl = useIntl();
  const { userData } = useAuth(); // Get user from auth context
  const [isPending, startTransition] = useTransition();

  const [questionnaire, setQuestionnaire] = useState<Questionnaire | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [tabValue, setTabValue] = useState<number>(0);

  const [patientInfo, setPatientInfo] = useState<TRQUser | null>(null);
  const [clientInfo, setClientInfo] = useState<TRQUser | null>(null);
  const [patientLoading, setPatientLoading] = useState<boolean>(false);
  const [clientLoading, setClientLoading] = useState<boolean>(false);
  const [patientError, setPatientError] = useState<string | null>(null);
  const [clientError, setClientError] = useState<string | null>(null);

  useEffect(() => {
    const fetchQuestionnaire = async () => {
      if (!id) {
        setError(intl.formatMessage({ id: 'questionnaire-id-not-provided' }) || 'Questionnaire ID not provided');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const questionnaireData = await getQuestionnaireById(id);
        if (!questionnaireData) {
          setError(intl.formatMessage({ id: 'questionnaire-not-found' }) || 'Questionnaire not found');
        } else {
          setQuestionnaire(questionnaireData);
        }
      } catch (error) {
        console.error('Error fetching questionnaire:', error);
        setError(
          intl.formatMessage({ id: 'error-fetching-questionnaire' }, { error: (error as Error).message }) || 'Error fetching questionnaire'
        );
      } finally {
        setLoading(false);
      }
    };

    fetchQuestionnaire();
  }, [id, intl]);

  useEffect(() => {
    if (questionnaire) {
      // Fetch Patient using UID
      const patientUid = questionnaire.assignedTo?.patientUid || questionnaire.assignedPatient;
      if (patientUid) {
        setPatientLoading(true);
        getUserById(patientUid)
          .then((user) => setPatientInfo(user))
          .catch(() => setPatientError('Failed to load patient information'))
          .finally(() => setPatientLoading(false));
      } else {
        setPatientInfo(null);
      }
      // Fetch Client using UID
      if (questionnaire.clientId) {
        setClientLoading(true);
        getUserById(questionnaire.clientId)
          .then((user) => setClientInfo(user))
          .catch(() => setClientError('Failed to load client information'))
          .finally(() => setClientLoading(false));
      } else {
        setClientInfo(null);
      }
    }
  }, [questionnaire]);

  const handleBack = () => {
    startTransition(() => {
      navigate(-1);
    });
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // --- Determine patient permissions ---
  const isPatient = userData?.role === Role.Patient;
  const isAssignedPatient = isPatient && patientInfo && userData?.uid === patientInfo.uid;

  if (loading || isPending) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh', flexDirection: 'column' }}>
        <Typography variant="h4" color="error" gutterBottom>
          {error}
        </Typography>
        <Button variant="contained" color="primary" startIcon={<ArrowBackIcon />} onClick={handleBack}>
          {intl.formatMessage({ id: 'back-to-questionnaires' }) || 'Back to Questionnaires'}
        </Button>
      </Box>
    );
  }

  if (!questionnaire) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh', flexDirection: 'column' }}>
        <Typography variant="h4" color="error" gutterBottom>
          {intl.formatMessage({ id: 'questionnaire-not-found' }) || 'Questionnaire not found'}
        </Typography>
        <Button variant="contained" color="primary" startIcon={<ArrowBackIcon />} onClick={handleBack}>
          {intl.formatMessage({ id: 'back-to-questionnaires' }) || 'Back to Questionnaires'}
        </Button>
      </Box>
    );
  }

  return (
    <Grid container spacing={gridSpacing}>
      <Grid item xs={12}>
        <Breadcrumbs
          custom
          heading={questionnaire.title}
          links={[
            // Role is guaranteed by TRQUser type if user exists
            { title: intl.formatMessage({ id: 'home' }) || 'Home', to: getDashboardUrlForRole(userData?.role) }, // Removed cast
            { title: intl.formatMessage({ id: 'questionnaires' }) || 'Questionnaires', to: '/trq/questionnaires' },
            { title: questionnaire.title }
          ]}
        />
      </Grid>

      <Grid item xs={12}>
        <MainCard>
          <Grid container spacing={gridSpacing}>
            <Grid item xs={12}>
              <Stack direction="row" justifyContent="space-between" alignItems="center">
                <Stack direction="row" spacing={2} alignItems="center">
                  <Typography variant="h3">{questionnaire.title}</Typography>
                  <Chip label={questionnaire.status} color={getStatusColor(questionnaire.status)} size="small" />
                </Stack>
                <Stack direction="row" spacing={1}>
                  <Button variant="outlined" color="primary" startIcon={<ArrowBackIcon />} onClick={handleBack}>
                    {intl.formatMessage({ id: 'back' }) || 'Back'}
                  </Button>
                  {/* Show Edit for non-patients, Start/Resume for assigned patient */}
                  {!isPatient && (
                    <Button variant="contained" color="primary" startIcon={<EditIcon />}>
                      {intl.formatMessage({ id: 'edit' }) || 'Edit'}
                    </Button>
                  )}
                  {isAssignedPatient && (
                    <Button
                      variant="contained"
                      color="primary"
                      startIcon={<CheckCircleIcon />}
                      onClick={() => navigate(ROUTES.QUESTIONNAIRES.WIZARD(questionnaire.id))}
                    >
                      {questionnaire.status === 'completed'
                        ? intl.formatMessage({ id: 'view' }) || 'View'
                        : questionnaire.status === 'in-progress'
                          ? intl.formatMessage({ id: 'resume' }) || 'Resume'
                          : intl.formatMessage({ id: 'start' }) || 'Start'}
                    </Button>
                  )}
                </Stack>
              </Stack>
            </Grid>

            <Grid item xs={12}>
              <Tabs value={tabValue} onChange={handleTabChange} aria-label="questionnaire tabs">
                <Tab icon={<AssignmentIcon />} label={intl.formatMessage({ id: 'details' }) || 'Details'} id="questionnaire-tab-0" />
                <Tab icon={<PersonIcon />} label={intl.formatMessage({ id: 'patient' }) || 'Patient'} id="questionnaire-tab-1" />
                <Tab icon={<BusinessIcon />} label={intl.formatMessage({ id: 'client' }) || 'Client'} id="questionnaire-tab-2" />
              </Tabs>
              <Divider />
            </Grid>

            <Grid item xs={12}>
              <TabPanel value={tabValue} index={0}>
                <Grid container spacing={gridSpacing}>
                  <Grid item xs={12} md={6}>
                    <SubCard title={intl.formatMessage({ id: 'questionnaire-info' }) || 'Questionnaire Information'}>
                      <Grid container spacing={2}>
                        <Grid item xs={12}>
                          <Stack spacing={1}>
                            <Typography variant="subtitle2" color="textSecondary">
                              {intl.formatMessage({ id: 'description' }) || 'Description'}
                            </Typography>
                            <Typography variant="body2">{questionnaire.description}</Typography>
                          </Stack>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <Stack spacing={1}>
                            <Typography variant="subtitle2" color="textSecondary">
                              {intl.formatMessage({ id: 'created-at' }) || 'Created At'}
                            </Typography>
                            <Typography variant="body2">{formatTimestamp(questionnaire.createdAt)}</Typography>
                          </Stack>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <Stack spacing={1}>
                            <Typography variant="subtitle2" color="textSecondary">
                              {intl.formatMessage({ id: 'status' }) || 'Status'}
                            </Typography>
                            <Chip label={questionnaire.status} color={getStatusColor(questionnaire.status)} size="small" />
                          </Stack>
                        </Grid>
                        {/* Added Template Info Section */}
                        <Grid item xs={12}>
                          <Stack spacing={1}>
                            <Typography variant="subtitle2" color="textSecondary">
                              {intl.formatMessage({ id: 'template-info' }) || 'Template Information'}
                            </Typography>
                            {questionnaire.template ? (
                              <Stack spacing={0.5}>
                                <Typography variant="body2">
                                  <strong>{intl.formatMessage({ id: 'template-name' }) || 'Name'}:</strong>{' '}
                                  {questionnaire.template.name || '-'}
                                </Typography>
                                <Typography variant="body2">
                                  <strong>{intl.formatMessage({ id: 'template-title' }) || 'Title'}:</strong>{' '}
                                  {questionnaire.template.title || '-'}
                                </Typography>
                                <Typography variant="body2">
                                  <strong>{intl.formatMessage({ id: 'template-version' }) || 'Version'}:</strong>{' '}
                                  {questionnaire.template.version ?? '-'}
                                </Typography>
                                <Typography variant="body2">
                                  <strong>{intl.formatMessage({ id: 'template-category' }) || 'Category'}:</strong>{' '}
                                  {questionnaire.template.category || '-'}
                                </Typography>
                                <Typography variant="body2">
                                  <strong>{intl.formatMessage({ id: 'template-published' }) || 'Published'}:</strong>{' '}
                                  {questionnaire.template.isPublished
                                    ? intl.formatMessage({ id: 'published' }) || 'Published'
                                    : intl.formatMessage({ id: 'draft' }) || 'Draft'}
                                </Typography>
                              </Stack>
                            ) : (
                              <Typography variant="body2" color="textSecondary">
                                {intl.formatMessage({ id: 'no-template-info' }) || 'No template information available.'}
                              </Typography>
                            )}
                          </Stack>
                        </Grid>
                      </Grid>
                    </SubCard>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <SubCard title={intl.formatMessage({ id: 'timeline' }) || 'Timeline'}>
                      <Stack spacing={2}>
                        <Stack direction="row" spacing={2} alignItems="center">
                          <AccessTimeIcon color="action" />
                          <Stack>
                            <Typography variant="subtitle2">{intl.formatMessage({ id: 'created' }) || 'Created'}</Typography>
                            <Typography variant="body2" color="textSecondary">
                              {formatTimestamp(questionnaire.createdAt)}
                            </Typography>
                          </Stack>
                        </Stack>
                        {questionnaire.startedAt && (
                          <Stack direction="row" spacing={2} alignItems="center">
                            <AccessTimeIcon color="action" />
                            <Stack>
                              <Typography variant="subtitle2">{intl.formatMessage({ id: 'started' }) || 'Started'}</Typography>
                              <Typography variant="body2" color="textSecondary">
                                {formatTimestamp(questionnaire.startedAt)}
                              </Typography>
                            </Stack>
                          </Stack>
                        )}
                        {questionnaire.completedAt && (
                          <Stack direction="row" spacing={2} alignItems="center">
                            <CheckCircleIcon color="success" />
                            <Stack>
                              <Typography variant="subtitle2">{intl.formatMessage({ id: 'completed' }) || 'Completed'}</Typography>
                              <Typography variant="body2" color="textSecondary">
                                {formatTimestamp(questionnaire.completedAt)}
                              </Typography>
                            </Stack>
                          </Stack>
                        )}
                      </Stack>
                    </SubCard>
                  </Grid>
                </Grid>
              </TabPanel>

              <TabPanel value={tabValue} index={1}>
                <SubCard title={intl.formatMessage({ id: 'patient-info' }) || 'Patient Information'}>
                  {patientLoading ? (
                    <CircularProgress size={24} />
                  ) : patientError ? (
                    <Typography variant="body2" color="error">
                      {patientError}
                    </Typography>
                  ) : patientInfo ? (
                    <Stack spacing={1}>
                      <Typography variant="subtitle2">
                        {`${patientInfo.firstName || ''} ${patientInfo.lastName || ''}`.trim() || '-'}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        <strong>{intl.formatMessage({ id: 'email' }) || 'Email'}:</strong> {patientInfo.email || '-'}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        <strong>{intl.formatMessage({ id: 'phone' }) || 'Phone'}:</strong> {patientInfo.phone || '-'}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        <strong>{intl.formatMessage({ id: 'role' }) || 'Role'}:</strong> {patientInfo.role || '-'}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        <strong>{intl.formatMessage({ id: 'address' }) || 'Address'}:</strong>{' '}
                        {patientInfo.address || intl.formatMessage({ id: 'not-provided' }) || 'Not provided'}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        <strong>{intl.formatMessage({ id: 'date-of-birth' }) || 'Date of Birth'}:</strong>{' '}
                        {/*
                          Removed dateOfBirth because it does not exist on TRQUser type.
                          If dateOfBirth is needed, it should be added to the TRQUser type and fetched accordingly.
                          For now, show placeholder.
                        */}
                        {intl.formatMessage({ id: 'not-provided' }) || 'Not provided'}
                      </Typography>
                    </Stack>
                  ) : (
                    <Typography variant="body2" color="textSecondary">
                      {intl.formatMessage({ id: 'no-patient-assigned' }) || 'No patient assigned.'}
                    </Typography>
                  )}
                </SubCard>
              </TabPanel>

              <TabPanel value={tabValue} index={2}>
                <SubCard title={intl.formatMessage({ id: 'client-info' }) || 'Client Information'}>
                  {clientLoading ? (
                    <CircularProgress size={24} />
                  ) : clientError ? (
                    <Typography variant="body2" color="error">
                      {clientError}
                    </Typography>
                  ) : clientInfo ? (
                    <Stack spacing={1}>
                      <Typography variant="subtitle2">
                        {`${clientInfo.firstName || ''} ${clientInfo.lastName || ''}`.trim() || '-'}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        <strong>{intl.formatMessage({ id: 'email' }) || 'Email'}:</strong> {clientInfo.email || '-'}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        <strong>{intl.formatMessage({ id: 'phone' }) || 'Phone'}:</strong> {clientInfo.phone || '-'}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        <strong>{intl.formatMessage({ id: 'role' }) || 'Role'}:</strong> {clientInfo.role || '-'}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        <strong>{intl.formatMessage({ id: 'company' }) || 'Company'}:</strong>{' '}
                        {clientInfo.asClient?.companyName || intl.formatMessage({ id: 'not-provided' }) || 'Not provided'}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        <strong>{intl.formatMessage({ id: 'address' }) || 'Address'}:</strong>{' '}
                        {clientInfo.address || intl.formatMessage({ id: 'not-provided' }) || 'Not provided'}
                      </Typography>
                    </Stack>
                  ) : (
                    <Typography variant="body2" color="textSecondary">
                      {intl.formatMessage({ id: 'no-client-assigned' }) || 'No client assigned.'}
                    </Typography>
                  )}
                </SubCard>
              </TabPanel>
            </Grid>
          </Grid>
        </MainCard>
      </Grid>
    </Grid>
  );
};

export default QuestionnaireDetails;
