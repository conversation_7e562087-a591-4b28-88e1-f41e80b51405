import React, { useState } from 'react';
import { useIntl } from 'react-intl';
import { useNavigate } from 'react-router-dom';

// material-ui
import {
  <PERSON><PERSON>,
  CardContent,
  CardHeader,
  Divider,
  Grid,
  Stack,
  TextField,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Snackbar,
  SelectChangeEvent
} from '@mui/material';

// project imports
import MainCard from '../../[components]/cards/MainCard';
import { gridSpacing } from '../../[constants]/gridSpacing';
import { createQuestionnaire } from '../[services]/questionnaireService';
import { Questionnaire, QuestionnaireStatus } from 'Questionnaires/[types]/Questionnaire';

// icons
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import SaveIcon from '@mui/icons-material/Save';

const CreateQuestionnaire = () => {
  const intl = useIntl();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [formData, setFormData] = useState<Partial<Questionnaire>>({
    name: '',
    title: '',
    description: '',
    status: QuestionnaireStatus.Created,
    isReviewed: false,
    questions: []
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value
    }));
  };

  const handleStatusChange = (e: SelectChangeEvent<string>) => {
    setFormData((prev) => ({
      ...prev,
      status: e.target.value as QuestionnaireStatus
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      await createQuestionnaire(formData as Omit<Questionnaire, 'id'>);
      setSuccess(true);
      // Wait for the success message to be shown before navigating
      setTimeout(() => {
        navigate('/trq/questionnaires');
      }, 2000);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while creating the questionnaire');
    } finally {
      setLoading(false);
    }
  };

  const handleCloseSnackbar = () => {
    setSuccess(false);
  };

  return (
    <Grid container spacing={gridSpacing}>
      <Grid item xs={12}>
        <MainCard>
          <CardHeader
            title={
              <Stack direction="row" alignItems="center" spacing={2}>
                <Button startIcon={<ArrowBackIcon />} onClick={() => navigate('/trq/questionnaires')}>
                  {intl.formatMessage({ id: 'back' }) || 'Back'}
                </Button>
                <Typography variant="h3">{intl.formatMessage({ id: 'create-questionnaire' }) || 'Create Questionnaire'}</Typography>
              </Stack>
            }
          />
          <Divider />
          <CardContent>
            <form onSubmit={handleSubmit}>
              <Grid container spacing={3}>
                {error && (
                  <Grid item xs={12}>
                    <Alert severity="error">{error}</Alert>
                  </Grid>
                )}

                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    required
                    label={intl.formatMessage({ id: 'name' }) || 'Name'}
                    name="name"
                    value={formData.name || ''}
                    onChange={handleChange}
                    inputProps={{ 'data-testid': 'questionnaire-name-input' }}
                  />
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    required
                    label={intl.formatMessage({ id: 'title' }) || 'Title'}
                    name="title"
                    value={formData.title || ''}
                    onChange={handleChange}
                    inputProps={{ 'data-testid': 'questionnaire-title-input' }}
                  />
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    multiline
                    rows={4}
                    label={intl.formatMessage({ id: 'description' }) || 'Description'}
                    name="description"
                    value={formData.description || ''}
                    onChange={handleChange}
                    inputProps={{ 'data-testid': 'questionnaire-description-input' }}
                  />
                </Grid>

                <Grid item xs={12}>
                  <FormControl fullWidth>
                    <InputLabel>{intl.formatMessage({ id: 'status' }) || 'Status'}</InputLabel>
                    <Select
                      name="status"
                      value={formData.status || QuestionnaireStatus.Created}
                      label={intl.formatMessage({ id: 'status' }) || 'Status'}
                      onChange={handleStatusChange}
                    >
                      <MenuItem value={QuestionnaireStatus.Created}>
                        {intl.formatMessage({ id: 'enums.questionnaire_status.created' }) || 'Created'}
                      </MenuItem>
                      <MenuItem value={QuestionnaireStatus.InProgress}>
                        {intl.formatMessage({ id: 'enums.questionnaire_status.in_progress' }) || 'In Progress'}
                      </MenuItem>
                      <MenuItem value={QuestionnaireStatus.Completed}>
                        {intl.formatMessage({ id: 'enums.questionnaire_status.completed' }) || 'Completed'}
                      </MenuItem>
                      <MenuItem value={QuestionnaireStatus.Reviewed}>
                        {intl.formatMessage({ id: 'enums.questionnaire_status.reviewed' }) || 'Reviewed'}
                      </MenuItem>
                      <MenuItem value={QuestionnaireStatus.Assigned}>
                        {intl.formatMessage({ id: 'enums.questionnaire_status.assigned' }) || 'Assigned'}
                      </MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12}>
                  <Stack direction="row" spacing={2} justifyContent="flex-end">
                    <Button variant="outlined" onClick={() => navigate('/trq/questionnaires')}>
                      {intl.formatMessage({ id: 'cancel' }) || 'Cancel'}
                    </Button>
                    <Button
                      type="submit"
                      variant="contained"
                      color="primary"
                      startIcon={<SaveIcon />}
                      disabled={loading || !formData.name || !formData.title}
                      data-testid="questionnaire-save-button"
                    >
                      {loading ? intl.formatMessage({ id: 'saving' }) || 'Saving...' : intl.formatMessage({ id: 'save' }) || 'Save'}
                    </Button>
                  </Stack>
                </Grid>
              </Grid>
            </form>
          </CardContent>
        </MainCard>
      </Grid>

      <Snackbar
        open={success}
        autoHideDuration={2000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert onClose={handleCloseSnackbar} severity="success" sx={{ width: '100%' }} data-testid="questionnaire-create-success-alert">
          {intl.formatMessage({ id: 'questionnaire-created-successfully' }) || 'Questionnaire created successfully'}
        </Alert>
      </Snackbar>
    </Grid>
  );
};

export default CreateQuestionnaire;
