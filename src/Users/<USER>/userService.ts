// User management service
// Handles CRUD operations for users

import { db as firestore } from '../../Firebase/[config]/firebase';
import { TRQUser } from '../[types]/User';
import {
  Timestamp,
  DocumentData,
  QueryDocumentSnapshot,
  doc,
  getDoc,
  setDoc,
  updateDoc,
  collection,
  query,
  where,
  getDocs,
  limit,
  orderBy,
  deleteDoc,
  DocumentSnapshot
} from 'firebase/firestore';
import { Role } from '../../RBAC/[types]/Role';
import { getAuth, User as FirebaseAuthUser } from 'firebase/auth';

// Collection references
const usersCollectionRef = collection(firestore, 'users');

/**
 * Convert Firestore document snapshot to TRQUser
 */
export const convertToTRQUser = (docSnap: DocumentSnapshot<DocumentData> | QueryDocumentSnapshot<DocumentData>): TRQUser => {
  const data = docSnap.data();
  if (!data) {
    throw new Error(`Document data is undefined for doc id: ${docSnap.id}`);
  }

  return {
    id: data.uid || docSnap.id,
    uid: data.uid || docSnap.id,
    email: data.email || '',
    firstName: data.firstName || '',
    lastName: data.lastName || '',
    role: data.role || Role.Patient,
    isActive: data.isActive !== undefined ? data.isActive : true,
    lastLogin: data.lastLogin || undefined,
    createdAt: data.createdAt || undefined,
    updatedAt: data.updatedAt || undefined,
    asDoctor: data.asDoctor || undefined,
    asPatient: data.asPatient || undefined,
    asClient: data.asClient || undefined,
    asClinicAdmin: data.asClinicAdmin || undefined,
    questionnaires: data.questionnaires || [],
    metadata: data.metadata || {},
    phone: data.phone || undefined,
    title: data.title || undefined,
    company: data.company || undefined,
    userName: data.userName || undefined,
    // Onboarding and invite tracking fields
    inviteEmailSentAt: data.inviteEmailSentAt || undefined,
    inviteEmailOpenedAt: data.inviteEmailOpenedAt || undefined,
    inviteLinkClickedAt: data.inviteLinkClickedAt || undefined,
    inviteEmailStatus: data.inviteEmailStatus || undefined,
    onboarded: data.onboarded || undefined
  };
};

/**
 * Get a user by their UID (Firestore Document ID)
 */
export const getUserByUid = async (uid: string): Promise<TRQUser | null> => {
  try {
    const userDocRef = doc(firestore, 'users', uid);
    const userDocSnap = await getDoc(userDocRef);

    if (!userDocSnap.exists()) {
      console.log(`No user found with UID: ${uid}`);
      return null;
    }

    return convertToTRQUser(userDocSnap);
  } catch (error) {
    console.error('Error getting user by UID:', error);
    throw error;
  }
};

// Alias for backward compatibility
export const getUserById = getUserByUid;

/**
 * Get a user by their email
 */
export const getUserByEmail = async (email: string): Promise<TRQUser | null> => {
  try {
    const q = query(usersCollectionRef, where('email', '==', email), limit(1));
    const querySnapshot = await getDocs(q);

    if (querySnapshot.empty) {
      console.log(`No user found with email: ${email}`);
      return null;
    }

    return convertToTRQUser(querySnapshot.docs[0]);
  } catch (error) {
    console.error('Error getting user by email:', error);
    throw error;
  }
};

/**
 * Get all users with optional filtering
 */
export const getUsers = async (
  options: {
    role?: Role;
    isActive?: boolean;
    clinicId?: string;
    doctorId?: string;
    clientId?: string;
    patientUids?: string[];
    limit?: number;
    orderByField?: string;
    orderDirection?: 'asc' | 'desc';
  } = {}
): Promise<TRQUser[]> => {
  try {
    // If patientUids is provided, use it to fetch users directly
    if (options.patientUids && Array.isArray(options.patientUids) && options.patientUids.length > 0) {
      return await getUsersByUids(options.patientUids);
    }
    const queryConstraints = [];

    if (options.role !== undefined) {
      queryConstraints.push(where('role', '==', options.role));
    }
    if (options.isActive !== undefined) {
      queryConstraints.push(where('isActive', '==', options.isActive));
    }
    if (options.clinicId) {
      if (options.role === Role.ClinicAdmin) {
        queryConstraints.push(where('asClinicAdmin.clinicId', '==', options.clinicId));
      } else {
        queryConstraints.push(where('asPatient.clinicId', '==', options.clinicId));
      }
    }
    if (options.doctorId) {
      queryConstraints.push(where('asPatient.doctorId', '==', options.doctorId));
    }
    if (options.clientId) {
      queryConstraints.push(where('asPatient.clientId', '==', options.clientId));
    }

    if (options.orderByField) {
      queryConstraints.push(orderBy(options.orderByField, options.orderDirection || 'asc'));
    }

    if (options.limit) {
      queryConstraints.push(limit(options.limit));
    }

    const q = query(usersCollectionRef, ...queryConstraints);
    const querySnapshot = await getDocs(q);

    const result = querySnapshot.docs.map((docSnap) => convertToTRQUser(docSnap));
    console.log('Users retrieved:', result);
    return result;
  } catch (error) {
    console.error('Error getting users:', error);
    throw error;
  }
};

/**
 * Utility: Get users by array of UIDs (up to 10 at a time, per Firestore limitation)
 */
export const getUsersByUids = async (uids: string[]): Promise<TRQUser[]> => {
  if (!uids || uids.length === 0) return [];
  const batches: TRQUser[] = [];
  for (let i = 0; i < uids.length; i += 10) {
    const batchUids = uids.slice(i, i + 10);
    const q = query(usersCollectionRef, where('__name__', 'in', batchUids));
    const querySnapshot = await getDocs(q);
    batches.push(...querySnapshot.docs.map((docSnap) => convertToTRQUser(docSnap)));
  }
  return batches;
};

/**
 * Utility: Get all patients for a client using the new model
 */
export const getPatientsForClient = async (clientId: string): Promise<TRQUser[]> => {
  const client = await getUserByUid(clientId);
  if (!client || !client.asClient || !Array.isArray(client.asClient.patients) || client.asClient.patients.length === 0) return [];
  return await getUsersByUids(client.asClient.patients);
};

/**
 * Create a new user in Firestore (use this after Firebase Auth user creation)
 */
export const createUser = async (userData: TRQUser): Promise<TRQUser> => {
  if (!userData.uid) {
    console.error('[createUser] No UID provided in userData:', userData);
    throw new Error('Cannot create user document without Firebase Auth UID (uid).');
  }
  try {
    const now = Timestamp.now();
    const userToCreate = {
      ...userData,
      createdAt: now,
      updatedAt: now
    };

    console.log(`[createUser] Attempting to create Firestore user:`, userToCreate);
    const userDocRef = doc(firestore, 'users', userData.uid);
    await setDoc(userDocRef, userToCreate);

    const newUserSnap = await getDoc(userDocRef);
    if (!newUserSnap.exists()) {
      console.error(`[createUser] Failed to create user document for UID: ${userData.uid}`);
      throw new Error('Failed to create user document after setDoc call.');
    }
    console.log(`[createUser] Successfully created Firestore user for UID: ${userData.uid}`);
    return convertToTRQUser(newUserSnap);
  } catch (error) {
    console.error('[createUser] Error creating user document:', error, userData);
    throw error;
  }
};

/**
 * Update an existing user
 */
export const updateUser = async (uid: string, userData: Partial<TRQUser>): Promise<TRQUser> => {
  try {
    const userRef = doc(firestore, 'users', uid);
    const now = Timestamp.now();

    const dataToUpdate = { ...userData };

    await updateDoc(userRef, {
      ...dataToUpdate,
      updatedAt: now
    });

    const updatedUserSnap = await getDoc(userRef);
    if (!updatedUserSnap.exists()) {
      throw new Error(`Failed to fetch updated user document: ${uid}`);
    }
    return convertToTRQUser(updatedUserSnap);
  } catch (error) {
    console.error('Error updating user:', error);
    throw error;
  }
};

/**
 * Delete a user from Firestore (use this alongside deleting Firebase Auth user)
 */
export const deleteUser = async (uid: string): Promise<void> => {
  try {
    const userDocRef = doc(firestore, 'users', uid);
    await deleteDoc(userDocRef);
  } catch (error) {
    console.error('Error deleting user document:', error);
    throw error;
  }
};

/**
 * Create or update user (upsert) using Firestore ID
 */
export const upsertUser = async (uid: string, userData: Partial<Omit<TRQUser, 'uid'>>): Promise<TRQUser> => {
  try {
    const userRef = doc(firestore, 'users', uid);
    const userDocSnap = await getDoc(userRef);
    const now = Timestamp.now();

    const dataToProcess = { ...userData };

    if (userDocSnap.exists()) {
      // Never allow role to be updated via upsert
      let updateData = { ...dataToProcess };
      delete updateData.role;
      await updateDoc(userRef, {
        ...updateData,
        updatedAt: now
      });
    } else {
      // Always use the uid parameter for new user creation
      await setDoc(userRef, {
        ...dataToProcess,
        uid,
        createdAt: now,
        updatedAt: now
      });
    }

    const finalUserSnap = await getDoc(userRef);
    if (!finalUserSnap.exists()) {
      throw new Error(`Failed to fetch user document after upsert: ${uid}`);
    }
    return convertToTRQUser(finalUserSnap);
  } catch (error) {
    console.error('Error upserting user:', error);
    throw error;
  }
};

/**
 * Ensures a Firestore user document exists for a given Firebase Auth user,
 * and creates one if it doesn't exist.
 *
 * @param authUser The Firebase Auth user object
 * @param defaultRole The default role to assign if creating a new user
 * @returns Promise resolving to the existing or newly created TRQUser
 */
export const ensureUserExists = async (authUser: FirebaseAuthUser, defaultRole: Role = Role.Patient): Promise<TRQUser> => {
  if (!authUser || !authUser.uid) {
    throw new Error('Invalid auth user provided');
  }
  try {
    // Try to get the user from Firestore
    const userDocRef = doc(firestore, 'users', authUser.uid);
    const userDocSnap = await getDoc(userDocRef);
    if (userDocSnap.exists()) {
      const userData = convertToTRQUser(userDocSnap);
      console.log('=== ensureUserExists: Found existing user ===');
      console.log('User data from Firestore:', userData);
      console.log('User role:', userData.role);
      console.log('User role type:', typeof userData.role);
      return userData;
    }
    // If user doesn't exist, create a default one
    // Extract name from displayName or email
    let firstName = '';
    let lastName = '';
    if (authUser.displayName) {
      const nameParts = authUser.displayName.split(' ');
      firstName = nameParts[0] || '';
      lastName = nameParts.slice(1).join(' ') || '';
    } else if (authUser.email) {
      firstName = authUser.email.split('@')[0] || '';
    }
    const now = Timestamp.now();
    const defaultUser: Omit<
      TRQUser,
      | 'questionnaires'
      | 'metadata'
      | 'asDoctor'
      | 'asPatient'
      | 'asClient'
      | 'asClinicAdmin'
      | 'phone'
      | 'title'
      | 'company'
      | 'userName'
      | 'inviteEmailSentAt'
      | 'inviteEmailOpenedAt'
      | 'inviteLinkClickedAt'
      | 'inviteEmailStatus'
      | 'onboarded'
    > = {
      uid: authUser.uid,
      firstName,
      lastName,
      email: authUser.email || '',
      role: Role.Patient,
      isActive: true,
      createdAt: now,
      updatedAt: now,
      lastLogin: now
    };

    console.log('=== ensureUserExists: Creating new default user ===');
    console.log('Default user data:', defaultUser);
    console.log('Default role assigned:', defaultUser.role);
    console.log('Default role type:', typeof defaultUser.role);

    await setDoc(userDocRef, defaultUser);
    console.log(`[ensureUserExists] Created default user in Firestore for auth user: ${authUser.uid}`);

    const newUser = (await getUserByUid(authUser.uid)) as TRQUser;
    console.log('=== ensureUserExists: Retrieved newly created user ===');
    console.log('New user data:', newUser);
    console.log('New user role:', newUser.role);
    console.log('New user role type:', typeof newUser.role);

    return newUser;
  } catch (error) {
    console.error('[ensureUserExists] Error ensuring user exists:', error);
    throw error;
  }
};

/**
 * Get current logged-in user's Firestore data
 * Note: This assumes Firebase Auth state is managed elsewhere (e.g., AuthContext)
 * and relies on getting the current user from the auth instance.
 *
 * If the user exists in Firebase Auth but not in Firestore, it will create a default
 * Firestore user document.
 */
export const getCurrentUserData = async (): Promise<TRQUser | null> => {
  try {
    const auth = getAuth();
    const currentUser: FirebaseAuthUser | null = auth.currentUser;

    if (!currentUser) {
      console.log('No Firebase user currently logged in.');
      return null;
    }

    // First try to get the user normally
    const existingUser = await getUserByUid(currentUser.uid);

    // If user exists in Firebase Auth but not in Firestore, create a default one
    if (!existingUser) {
      console.log(`User exists in Firebase Auth but not in Firestore. Creating default user for ${currentUser.uid}`);

      // Create simple default user
      const now = Timestamp.now();

      // Extract name from displayName or email
      let firstName = '';
      let lastName = '';

      if (currentUser.displayName) {
        const nameParts = currentUser.displayName.split(' ');
        firstName = nameParts[0] || '';
        lastName = nameParts.slice(1).join(' ') || '';
      } else if (currentUser.email) {
        firstName = currentUser.email.split('@')[0] || '';
      }

      const defaultUser: TRQUser = {
        uid: currentUser.uid,
        firstName,
        lastName,
        email: currentUser.email || '',
        role: Role.Patient, // Default role
        isActive: true,
        createdAt: now,
        updatedAt: now,
        lastLogin: now
      };

      // Save to Firestore
      const userRef = doc(firestore, 'users', currentUser.uid);
      await setDoc(userRef, defaultUser);

      console.log(`Created default user in Firestore for auth user: ${currentUser.uid}`);
      return defaultUser;
    }

    return existingUser;
  } catch (error) {
    console.error('Error getting current user data:', error);
    throw error;
  }
};

// --- Onboarding Helper ---
/**
 * Mark a user as onboarded (after first successful login/password setup)
 */
export const markUserOnboarded = async (uid: string): Promise<void> => {
  const userRef = doc(firestore, 'users', uid);
  await updateDoc(userRef, {
    onboarded: true,
    updatedAt: Timestamp.now()
  });
};

/**
 * Get user counts by role, excluding Admins
 */
export const getUserCountsByRole = async (): Promise<Record<string, number>> => {
  const allUsers = await getUsers();
  const counts: Record<string, number> = {};
  allUsers.forEach((user) => {
    if (user.role && user.role !== Role.Admin) {
      counts[user.role] = (counts[user.role] || 0) + 1;
    }
  });
  return counts;
};
