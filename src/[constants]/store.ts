import { useDispatch as useReduxDispatch, useSelector as useReduxSelector } from 'react-redux';
import { TypedUseSelectorHook } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// Define types for snackbar state
export interface SnackbarProps {
  action: boolean;
  open: boolean;
  message: string;
  anchorOrigin: {
    vertical: string;
    horizontal: string;
  };
  variant: string;
  alert: {
    color: string;
    severity: 'success' | 'error' | 'warning' | 'info';
  };
  transition: string;
  close: boolean;
  maxStack: number;
}

// Define initial state for the snackbar
const initialState: SnackbarProps = {
  action: false,
  open: false,
  message: 'Note archived',
  anchorOrigin: {
    vertical: 'bottom',
    horizontal: 'right'
  },
  variant: 'default',
  alert: {
    color: 'primary',
    severity: 'info'
  },
  transition: 'Fade',
  close: true,
  maxStack: 3
};

// Create snackbar slice
const snackbarSlice = createSlice({
  name: 'snackbar',
  initialState,
  reducers: {
    openSnackbar: (
      state,
      action: PayloadAction<{
        open: boolean;
        message: string;
        variant?: string;
        alert?: { color: string; severity: 'success' | 'error' | 'warning' | 'info' };
      }>
    ) => {
      const { open, message, variant, alert } = action.payload;

      state.open = open;
      state.message = message;

      if (variant) {
        state.variant = variant;
      }

      if (alert) {
        state.alert = alert;
      }
    },
    handlerIncrease: (state, action: PayloadAction<{ maxStack: number }>) => {
      state.maxStack = action.payload.maxStack;
    }
  }
});

// Extract actions
export const { openSnackbar, handlerIncrease } = snackbarSlice.actions;

// Create root reducer
const rootReducer = {
  snackbar: snackbarSlice.reducer
};

// Configure store
export const store = configureStore({
  reducer: rootReducer
});

// Define RootState type
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// Create typed hooks
export const useDispatch = () => useReduxDispatch<AppDispatch>();
export const useSelector: TypedUseSelectorHook<RootState> = useReduxSelector;

// Export dispatch for direct use
export const { dispatch } = store;
