import { Timestamp } from 'firebase/firestore';

export enum PaymentStatus {
  Pending = 'pending',
  Completed = 'completed',
  Failed = 'failed',
  Refunded = 'refunded'
}

/**
 * Represents a purchase record in the TitaniumRQ system.
 */
export interface Purchase {
  id: string;
  clientId: string;
  clientName: string;
  questionnaires: {
    id: string;
    name: string;
    quantity: number;
    unitPrice: number;
  }[];
  totalQuantity: number;
  totalPrice: number;
  paymentStatus: PaymentStatus;
  purchaseDate: Timestamp;
  lastUpdated: Timestamp;
  paymentMethod?: string;
  transactionId?: string;
  notes?: string;
  templateId: string;
  questionnaireQuantity: number;
  unitPrice: number;
  subTotal: number;
  tax: number;
  isPaid: boolean;
  date: Timestamp;
  note?: string;
  paymentType: string;
}

export { Timestamp };
